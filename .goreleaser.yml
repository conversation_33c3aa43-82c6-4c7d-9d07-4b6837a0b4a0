---
version: 2
before:
  hooks:
    - go mod tidy -compat=1.24
    - go mod vendor

release:
  prerelease: auto

builds:
  - id: headscale
    main: ./cmd/headscale
    mod_timestamp: "{{ .CommitTimestamp }}"
    env:
      - CGO_ENABLED=0
    targets:
      - darwin_amd64
      - darwin_arm64
      - freebsd_amd64
      - linux_386
      - linux_amd64
      - linux_arm64
      - linux_arm_5
      - linux_arm_6
      - linux_arm_7
    flags:
      - -mod=readonly
    ldflags:
      - -s -w
      - -X github.com/juanfont/headscale/hscontrol/types.Version={{ .Version }}
      - -X github.com/juanfont/headscale/hscontrol/types.GitCommitHash={{ .Commit }}
    tags:
      - ts2019

archives:
  - id: golang-cross
    name_template: '{{ .ProjectName }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}{{ with .Arm }}v{{ . }}{{ end }}{{ with .Mips }}_{{ . }}{{ end }}{{ if not (eq .Amd64 "v1") }}{{ .Amd64 }}{{ end }}'
    formats:
      - binary

source:
  enabled: true
  name_template: "{{ .ProjectName }}_{{ .Version }}"
  format: tar.gz
  files:
    - "vendor/"

nfpms:
  # Configure nFPM for .deb and .rpm releases
  #
  # See https://nfpm.goreleaser.com/configuration/
  # and https://goreleaser.com/customization/nfpm/
  #
  # Useful tools for debugging .debs:
  # List file contents: dpkg -c dist/headscale...deb
  # Package metadata: dpkg --info dist/headscale....deb
  #
  - ids:
      - headscale
    package_name: headscale
    priority: optional
    vendor: headscale
    maintainer: Kristoffer Dalby <<EMAIL>>
    homepage: https://github.com/juanfont/headscale
    license: BSD
    bindir: /usr/bin
    formats:
      - deb
    contents:
      - src: ./config-example.yaml
        dst: /etc/headscale/config.yaml
        type: config|noreplace
        file_info:
          mode: 0644
      - src: ./docs/packaging/headscale.systemd.service
        dst: /usr/lib/systemd/system/headscale.service
      - dst: /var/lib/headscale
        type: dir
      - dst: /var/run/headscale
        type: dir
    scripts:
      postinstall: ./docs/packaging/postinstall.sh
      postremove: ./docs/packaging/postremove.sh

kos:
  - id: ghcr
    repositories:
      - ghcr.io/juanfont/headscale
      - headscale/headscale

    # bare tells KO to only use the repository
    # for tagging and naming the container.
    bare: true
    base_image: gcr.io/distroless/base-debian12
    build: headscale
    main: ./cmd/headscale
    env:
      - CGO_ENABLED=0
    platforms:
      - linux/amd64
      - linux/386
      - linux/arm64
      - linux/arm/v7
    tags:
      - "{{ if not .Prerelease }}latest{{ end }}"
      - "{{ if not .Prerelease }}{{ .Major }}.{{ .Minor }}.{{ .Patch }}{{ end }}"
      - "{{ if not .Prerelease }}{{ .Major }}.{{ .Minor }}{{ end }}"
      - "{{ if not .Prerelease }}{{ .Major }}{{ end }}"
      - "{{ if not .Prerelease }}v{{ .Major }}.{{ .Minor }}.{{ .Patch }}{{ end }}"
      - "{{ if not .Prerelease }}v{{ .Major }}.{{ .Minor }}{{ end }}"
      - "{{ if not .Prerelease }}v{{ .Major }}{{ end }}"
      - "{{ if not .Prerelease }}stable{{ else }}unstable{{ end }}"
      - "{{ .Tag }}"
      - '{{ trimprefix .Tag "v" }}'
      - "sha-{{ .ShortCommit }}"

  - id: ghcr-debug
    repositories:
      - ghcr.io/juanfont/headscale
      - headscale/headscale

    bare: true
    base_image: gcr.io/distroless/base-debian12:debug
    build: headscale
    main: ./cmd/headscale
    env:
      - CGO_ENABLED=0
    platforms:
      - linux/amd64
      - linux/386
      - linux/arm64
      - linux/arm/v7
    tags:
      - "{{ if not .Prerelease }}latest-debug{{ end }}"
      - "{{ if not .Prerelease }}{{ .Major }}.{{ .Minor }}.{{ .Patch }}-debug{{ end }}"
      - "{{ if not .Prerelease }}{{ .Major }}.{{ .Minor }}-debug{{ end }}"
      - "{{ if not .Prerelease }}{{ .Major }}-debug{{ end }}"
      - "{{ if not .Prerelease }}v{{ .Major }}.{{ .Minor }}.{{ .Patch }}-debug{{ end }}"
      - "{{ if not .Prerelease }}v{{ .Major }}.{{ .Minor }}-debug{{ end }}"
      - "{{ if not .Prerelease }}v{{ .Major }}-debug{{ end }}"
      - "{{ if not .Prerelease }}stable-debug{{ else }}unstable-debug{{ end }}"
      - "{{ .Tag }}-debug"
      - '{{ trimprefix .Tag "v" }}-debug'
      - "sha-{{ .ShortCommit }}-debug"

checksum:
  name_template: "checksums.txt"
snapshot:
  version_template: "{{ .Tag }}-next"
changelog:
  sort: asc
  filters:
    exclude:
      - "^docs:"
      - "^test:"
