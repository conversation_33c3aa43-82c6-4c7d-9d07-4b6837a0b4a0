// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: headscale/v1/apikey.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ApiKey struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Prefix        string                 `protobuf:"bytes,2,opt,name=prefix,proto3" json:"prefix,omitempty"`
	Expiration    *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=expiration,proto3" json:"expiration,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	LastSeen      *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=last_seen,json=lastSeen,proto3" json:"last_seen,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApiKey) Reset() {
	*x = ApiKey{}
	mi := &file_headscale_v1_apikey_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApiKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiKey) ProtoMessage() {}

func (x *ApiKey) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_apikey_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiKey.ProtoReflect.Descriptor instead.
func (*ApiKey) Descriptor() ([]byte, []int) {
	return file_headscale_v1_apikey_proto_rawDescGZIP(), []int{0}
}

func (x *ApiKey) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ApiKey) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *ApiKey) GetExpiration() *timestamppb.Timestamp {
	if x != nil {
		return x.Expiration
	}
	return nil
}

func (x *ApiKey) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ApiKey) GetLastSeen() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSeen
	}
	return nil
}

type CreateApiKeyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Expiration    *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=expiration,proto3" json:"expiration,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateApiKeyRequest) Reset() {
	*x = CreateApiKeyRequest{}
	mi := &file_headscale_v1_apikey_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateApiKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateApiKeyRequest) ProtoMessage() {}

func (x *CreateApiKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_apikey_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateApiKeyRequest.ProtoReflect.Descriptor instead.
func (*CreateApiKeyRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_apikey_proto_rawDescGZIP(), []int{1}
}

func (x *CreateApiKeyRequest) GetExpiration() *timestamppb.Timestamp {
	if x != nil {
		return x.Expiration
	}
	return nil
}

type CreateApiKeyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiKey        string                 `protobuf:"bytes,1,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateApiKeyResponse) Reset() {
	*x = CreateApiKeyResponse{}
	mi := &file_headscale_v1_apikey_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateApiKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateApiKeyResponse) ProtoMessage() {}

func (x *CreateApiKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_apikey_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateApiKeyResponse.ProtoReflect.Descriptor instead.
func (*CreateApiKeyResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_apikey_proto_rawDescGZIP(), []int{2}
}

func (x *CreateApiKeyResponse) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

type ExpireApiKeyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Prefix        string                 `protobuf:"bytes,1,opt,name=prefix,proto3" json:"prefix,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExpireApiKeyRequest) Reset() {
	*x = ExpireApiKeyRequest{}
	mi := &file_headscale_v1_apikey_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExpireApiKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpireApiKeyRequest) ProtoMessage() {}

func (x *ExpireApiKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_apikey_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpireApiKeyRequest.ProtoReflect.Descriptor instead.
func (*ExpireApiKeyRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_apikey_proto_rawDescGZIP(), []int{3}
}

func (x *ExpireApiKeyRequest) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

type ExpireApiKeyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExpireApiKeyResponse) Reset() {
	*x = ExpireApiKeyResponse{}
	mi := &file_headscale_v1_apikey_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExpireApiKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpireApiKeyResponse) ProtoMessage() {}

func (x *ExpireApiKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_apikey_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpireApiKeyResponse.ProtoReflect.Descriptor instead.
func (*ExpireApiKeyResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_apikey_proto_rawDescGZIP(), []int{4}
}

type ListApiKeysRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListApiKeysRequest) Reset() {
	*x = ListApiKeysRequest{}
	mi := &file_headscale_v1_apikey_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListApiKeysRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApiKeysRequest) ProtoMessage() {}

func (x *ListApiKeysRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_apikey_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApiKeysRequest.ProtoReflect.Descriptor instead.
func (*ListApiKeysRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_apikey_proto_rawDescGZIP(), []int{5}
}

type ListApiKeysResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ApiKeys       []*ApiKey              `protobuf:"bytes,1,rep,name=api_keys,json=apiKeys,proto3" json:"api_keys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListApiKeysResponse) Reset() {
	*x = ListApiKeysResponse{}
	mi := &file_headscale_v1_apikey_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListApiKeysResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApiKeysResponse) ProtoMessage() {}

func (x *ListApiKeysResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_apikey_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApiKeysResponse.ProtoReflect.Descriptor instead.
func (*ListApiKeysResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_apikey_proto_rawDescGZIP(), []int{6}
}

func (x *ListApiKeysResponse) GetApiKeys() []*ApiKey {
	if x != nil {
		return x.ApiKeys
	}
	return nil
}

type DeleteApiKeyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Prefix        string                 `protobuf:"bytes,1,opt,name=prefix,proto3" json:"prefix,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteApiKeyRequest) Reset() {
	*x = DeleteApiKeyRequest{}
	mi := &file_headscale_v1_apikey_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteApiKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteApiKeyRequest) ProtoMessage() {}

func (x *DeleteApiKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_apikey_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteApiKeyRequest.ProtoReflect.Descriptor instead.
func (*DeleteApiKeyRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_apikey_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteApiKeyRequest) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

type DeleteApiKeyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteApiKeyResponse) Reset() {
	*x = DeleteApiKeyResponse{}
	mi := &file_headscale_v1_apikey_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteApiKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteApiKeyResponse) ProtoMessage() {}

func (x *DeleteApiKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_apikey_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteApiKeyResponse.ProtoReflect.Descriptor instead.
func (*DeleteApiKeyResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_apikey_proto_rawDescGZIP(), []int{8}
}

var File_headscale_v1_apikey_proto protoreflect.FileDescriptor

const file_headscale_v1_apikey_proto_rawDesc = "" +
	"\n" +
	"\x19headscale/v1/apikey.proto\x12\fheadscale.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\xe0\x01\n" +
	"\x06ApiKey\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x16\n" +
	"\x06prefix\x18\x02 \x01(\tR\x06prefix\x12:\n" +
	"\n" +
	"expiration\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"expiration\x129\n" +
	"\n" +
	"created_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x127\n" +
	"\tlast_seen\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\blastSeen\"Q\n" +
	"\x13CreateApiKeyRequest\x12:\n" +
	"\n" +
	"expiration\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"expiration\"/\n" +
	"\x14CreateApiKeyResponse\x12\x17\n" +
	"\aapi_key\x18\x01 \x01(\tR\x06apiKey\"-\n" +
	"\x13ExpireApiKeyRequest\x12\x16\n" +
	"\x06prefix\x18\x01 \x01(\tR\x06prefix\"\x16\n" +
	"\x14ExpireApiKeyResponse\"\x14\n" +
	"\x12ListApiKeysRequest\"F\n" +
	"\x13ListApiKeysResponse\x12/\n" +
	"\bapi_keys\x18\x01 \x03(\v2\x14.headscale.v1.ApiKeyR\aapiKeys\"-\n" +
	"\x13DeleteApiKeyRequest\x12\x16\n" +
	"\x06prefix\x18\x01 \x01(\tR\x06prefix\"\x16\n" +
	"\x14DeleteApiKeyResponseB)Z'github.com/juanfont/headscale/gen/go/v1b\x06proto3"

var (
	file_headscale_v1_apikey_proto_rawDescOnce sync.Once
	file_headscale_v1_apikey_proto_rawDescData []byte
)

func file_headscale_v1_apikey_proto_rawDescGZIP() []byte {
	file_headscale_v1_apikey_proto_rawDescOnce.Do(func() {
		file_headscale_v1_apikey_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_headscale_v1_apikey_proto_rawDesc), len(file_headscale_v1_apikey_proto_rawDesc)))
	})
	return file_headscale_v1_apikey_proto_rawDescData
}

var file_headscale_v1_apikey_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_headscale_v1_apikey_proto_goTypes = []any{
	(*ApiKey)(nil),                // 0: headscale.v1.ApiKey
	(*CreateApiKeyRequest)(nil),   // 1: headscale.v1.CreateApiKeyRequest
	(*CreateApiKeyResponse)(nil),  // 2: headscale.v1.CreateApiKeyResponse
	(*ExpireApiKeyRequest)(nil),   // 3: headscale.v1.ExpireApiKeyRequest
	(*ExpireApiKeyResponse)(nil),  // 4: headscale.v1.ExpireApiKeyResponse
	(*ListApiKeysRequest)(nil),    // 5: headscale.v1.ListApiKeysRequest
	(*ListApiKeysResponse)(nil),   // 6: headscale.v1.ListApiKeysResponse
	(*DeleteApiKeyRequest)(nil),   // 7: headscale.v1.DeleteApiKeyRequest
	(*DeleteApiKeyResponse)(nil),  // 8: headscale.v1.DeleteApiKeyResponse
	(*timestamppb.Timestamp)(nil), // 9: google.protobuf.Timestamp
}
var file_headscale_v1_apikey_proto_depIdxs = []int32{
	9, // 0: headscale.v1.ApiKey.expiration:type_name -> google.protobuf.Timestamp
	9, // 1: headscale.v1.ApiKey.created_at:type_name -> google.protobuf.Timestamp
	9, // 2: headscale.v1.ApiKey.last_seen:type_name -> google.protobuf.Timestamp
	9, // 3: headscale.v1.CreateApiKeyRequest.expiration:type_name -> google.protobuf.Timestamp
	0, // 4: headscale.v1.ListApiKeysResponse.api_keys:type_name -> headscale.v1.ApiKey
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_headscale_v1_apikey_proto_init() }
func file_headscale_v1_apikey_proto_init() {
	if File_headscale_v1_apikey_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_headscale_v1_apikey_proto_rawDesc), len(file_headscale_v1_apikey_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_headscale_v1_apikey_proto_goTypes,
		DependencyIndexes: file_headscale_v1_apikey_proto_depIdxs,
		MessageInfos:      file_headscale_v1_apikey_proto_msgTypes,
	}.Build()
	File_headscale_v1_apikey_proto = out.File
	file_headscale_v1_apikey_proto_goTypes = nil
	file_headscale_v1_apikey_proto_depIdxs = nil
}
