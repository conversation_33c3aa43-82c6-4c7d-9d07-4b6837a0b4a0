name: Tests

on: [push, pull_request]

concurrency:
  group: ${{ github.workflow }}-$${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Get changed files
        id: changed-files
        uses: dorny/paths-filter@v3
        with:
          filters: |
            files:
              - '*.nix'
              - 'go.*'
              - '**/*.go'
              - 'integration_test/'
              - 'config-example.yaml'

      - uses: DeterminateSystems/nix-installer-action@main
        if: steps.changed-files.outputs.files == 'true'
      - uses: DeterminateSystems/magic-nix-cache-action@main
        if: steps.changed-files.outputs.files == 'true'

      - name: Run tests
        if: steps.changed-files.outputs.files == 'true'
        env:
          # As of 2025-01-06, these env vars was not automatically
          # set anymore which breaks the initdb for postgres on
          # some of the database migration tests.
          LC_ALL: "en_US.UTF-8"
          LC_CTYPE: "en_US.UTF-8"
        run: nix develop --command -- gotestsum
