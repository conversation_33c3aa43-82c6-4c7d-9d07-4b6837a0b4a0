cd /mnt/c/Users/<USER>/Documents/develop/0me/headscale

git checkout -b release-v0.26.1 v0.26.1


# 检查是否有 result 目录
ls -la result/


make generate

nix develop
make test
make build


# 清理所有未被引用的 Nix store 路径
nix-collect-garbage


# 检查构建结果
ls -la ./result/bin/
file ./result/bin/headscale
ldd ./result/bin/headscale


sudo docker run --rm -it --entrypoint /bin/sh headscale:v0.26.1-r

sudo docker logs headscale



sudo docker run --rm -it --entrypoint /bin/sh ghcr.io/juanfont/headscale:v0.26.1

sudo docker run -d --name headscale -p 8080:8080 -p 9090:9090 headscale:v0.26.1-r

sudo docker rmi headscale:v0.26.1-r


djc@jetron-djc:/mnt/c/Users/<USER>/Documents/develop/0me/headscale$ ldd headscale 
        linux-vdso.so.1 (0x00007ffd207f9000)
        libresolv.so.2 => /lib/x86_64-linux-gnu/libresolv.so.2 (0x00007fe62fd2e000)
        libpthread.so.0 => /lib/x86_64-linux-gnu/libpthread.so.0 (0x00007fe62fd29000)
        libc.so.6 => /lib/x86_64-linux-gnu/libc.so.6 (0x00007fe62fb17000)
        /nix/store/vbrdc5wgzn0w1zdp10xd2favkjn5fk7y-glibc-2.40-66/lib/ld-linux-x86-64.so.2 => /lib64/ld-linux-x86-64.so.2 (0x00007fe62fd48000)

sudo mkdir -p /nix/store/vbrdc5wgzn0w1zdp10xd2favkjn5fk7y-glibc-2.40-66/lib
sudo cp ./ld-linux-x86-64.so.2 /nix/store/vbrdc5wgzn0w1zdp10xd2favkjn5fk7y-glibc-2.40-66/lib/


go install github.com/google/ko@latest
/home/<USER>/go/bin/ko build --local ./cmd/headscale

docker run -d -v ./config.yaml:/etc/headscale/config.yaml --name headscale -p 8080:8080 -p 9090:9090 headscale:v0.26.1-r serve
docker run -d -v .headscale/config.yaml:/etc/headscale/config.yaml -v ./doc:/var/lib/headscale --name headscale -p 8080:8080 -p 9090:9090 headscale:v0.26.1-r serve

测试：sudo docker run -d -v /home/<USER>/doc/headscale/config.yaml:/etc/headscale/config.yaml -v /var/lib/headscale:/var/lib/headscale --name headscale -p 8080:8080 -p 9090:9090 -p 50443:50443  headscale:v0.26.1-r serve