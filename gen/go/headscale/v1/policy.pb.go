// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: headscale/v1/policy.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SetPolicyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Policy        string                 `protobuf:"bytes,1,opt,name=policy,proto3" json:"policy,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetPolicyRequest) Reset() {
	*x = SetPolicyRequest{}
	mi := &file_headscale_v1_policy_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPolicyRequest) ProtoMessage() {}

func (x *SetPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_policy_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPolicyRequest.ProtoReflect.Descriptor instead.
func (*SetPolicyRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_policy_proto_rawDescGZIP(), []int{0}
}

func (x *SetPolicyRequest) GetPolicy() string {
	if x != nil {
		return x.Policy
	}
	return ""
}

type SetPolicyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Policy        string                 `protobuf:"bytes,1,opt,name=policy,proto3" json:"policy,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetPolicyResponse) Reset() {
	*x = SetPolicyResponse{}
	mi := &file_headscale_v1_policy_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPolicyResponse) ProtoMessage() {}

func (x *SetPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_policy_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPolicyResponse.ProtoReflect.Descriptor instead.
func (*SetPolicyResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_policy_proto_rawDescGZIP(), []int{1}
}

func (x *SetPolicyResponse) GetPolicy() string {
	if x != nil {
		return x.Policy
	}
	return ""
}

func (x *SetPolicyResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type GetPolicyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPolicyRequest) Reset() {
	*x = GetPolicyRequest{}
	mi := &file_headscale_v1_policy_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPolicyRequest) ProtoMessage() {}

func (x *GetPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_policy_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPolicyRequest.ProtoReflect.Descriptor instead.
func (*GetPolicyRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_policy_proto_rawDescGZIP(), []int{2}
}

type GetPolicyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Policy        string                 `protobuf:"bytes,1,opt,name=policy,proto3" json:"policy,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPolicyResponse) Reset() {
	*x = GetPolicyResponse{}
	mi := &file_headscale_v1_policy_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPolicyResponse) ProtoMessage() {}

func (x *GetPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_policy_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPolicyResponse.ProtoReflect.Descriptor instead.
func (*GetPolicyResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_policy_proto_rawDescGZIP(), []int{3}
}

func (x *GetPolicyResponse) GetPolicy() string {
	if x != nil {
		return x.Policy
	}
	return ""
}

func (x *GetPolicyResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_headscale_v1_policy_proto protoreflect.FileDescriptor

const file_headscale_v1_policy_proto_rawDesc = "" +
	"\n" +
	"\x19headscale/v1/policy.proto\x12\fheadscale.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"*\n" +
	"\x10SetPolicyRequest\x12\x16\n" +
	"\x06policy\x18\x01 \x01(\tR\x06policy\"f\n" +
	"\x11SetPolicyResponse\x12\x16\n" +
	"\x06policy\x18\x01 \x01(\tR\x06policy\x129\n" +
	"\n" +
	"updated_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x12\n" +
	"\x10GetPolicyRequest\"f\n" +
	"\x11GetPolicyResponse\x12\x16\n" +
	"\x06policy\x18\x01 \x01(\tR\x06policy\x129\n" +
	"\n" +
	"updated_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAtB)Z'github.com/juanfont/headscale/gen/go/v1b\x06proto3"

var (
	file_headscale_v1_policy_proto_rawDescOnce sync.Once
	file_headscale_v1_policy_proto_rawDescData []byte
)

func file_headscale_v1_policy_proto_rawDescGZIP() []byte {
	file_headscale_v1_policy_proto_rawDescOnce.Do(func() {
		file_headscale_v1_policy_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_headscale_v1_policy_proto_rawDesc), len(file_headscale_v1_policy_proto_rawDesc)))
	})
	return file_headscale_v1_policy_proto_rawDescData
}

var file_headscale_v1_policy_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_headscale_v1_policy_proto_goTypes = []any{
	(*SetPolicyRequest)(nil),      // 0: headscale.v1.SetPolicyRequest
	(*SetPolicyResponse)(nil),     // 1: headscale.v1.SetPolicyResponse
	(*GetPolicyRequest)(nil),      // 2: headscale.v1.GetPolicyRequest
	(*GetPolicyResponse)(nil),     // 3: headscale.v1.GetPolicyResponse
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
}
var file_headscale_v1_policy_proto_depIdxs = []int32{
	4, // 0: headscale.v1.SetPolicyResponse.updated_at:type_name -> google.protobuf.Timestamp
	4, // 1: headscale.v1.GetPolicyResponse.updated_at:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_headscale_v1_policy_proto_init() }
func file_headscale_v1_policy_proto_init() {
	if File_headscale_v1_policy_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_headscale_v1_policy_proto_rawDesc), len(file_headscale_v1_policy_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_headscale_v1_policy_proto_goTypes,
		DependencyIndexes: file_headscale_v1_policy_proto_depIdxs,
		MessageInfos:      file_headscale_v1_policy_proto_msgTypes,
	}.Build()
	File_headscale_v1_policy_proto = out.File
	file_headscale_v1_policy_proto_goTypes = nil
	file_headscale_v1_policy_proto_depIdxs = nil
}
