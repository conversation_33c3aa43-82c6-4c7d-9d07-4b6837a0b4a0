name: Test documentation build

on: [pull_request]

concurrency:
  group: ${{ github.workflow }}-$${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Install python
        uses: actions/setup-python@v5
        with:
          python-version: 3.x
      - name: Setup cache
        uses: actions/cache@v4
        with:
          key: ${{ github.ref }}
          path: .cache
      - name: Setup dependencies
        run: pip install -r docs/requirements.txt
      - name: Build docs
        run: mkdocs build --strict
