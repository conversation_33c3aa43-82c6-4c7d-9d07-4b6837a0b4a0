// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: headscale/v1/node.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RegisterMethod int32

const (
	RegisterMethod_REGISTER_METHOD_UNSPECIFIED RegisterMethod = 0
	RegisterMethod_REGISTER_METHOD_AUTH_KEY    RegisterMethod = 1
	RegisterMethod_REGISTER_METHOD_CLI         RegisterMethod = 2
	RegisterMethod_REGISTER_METHOD_OIDC        RegisterMethod = 3
)

// Enum value maps for RegisterMethod.
var (
	RegisterMethod_name = map[int32]string{
		0: "REGISTER_METHOD_UNSPECIFIED",
		1: "REGISTER_METHOD_AUTH_KEY",
		2: "REGISTER_METHOD_CLI",
		3: "REGISTER_METHOD_OIDC",
	}
	RegisterMethod_value = map[string]int32{
		"REGISTER_METHOD_UNSPECIFIED": 0,
		"REGISTER_METHOD_AUTH_KEY":    1,
		"REGISTER_METHOD_CLI":         2,
		"REGISTER_METHOD_OIDC":        3,
	}
)

func (x RegisterMethod) Enum() *RegisterMethod {
	p := new(RegisterMethod)
	*p = x
	return p
}

func (x RegisterMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RegisterMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_headscale_v1_node_proto_enumTypes[0].Descriptor()
}

func (RegisterMethod) Type() protoreflect.EnumType {
	return &file_headscale_v1_node_proto_enumTypes[0]
}

func (x RegisterMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RegisterMethod.Descriptor instead.
func (RegisterMethod) EnumDescriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{0}
}

type Node struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MachineKey      string                 `protobuf:"bytes,2,opt,name=machine_key,json=machineKey,proto3" json:"machine_key,omitempty"`
	NodeKey         string                 `protobuf:"bytes,3,opt,name=node_key,json=nodeKey,proto3" json:"node_key,omitempty"`
	DiscoKey        string                 `protobuf:"bytes,4,opt,name=disco_key,json=discoKey,proto3" json:"disco_key,omitempty"`
	IpAddresses     []string               `protobuf:"bytes,5,rep,name=ip_addresses,json=ipAddresses,proto3" json:"ip_addresses,omitempty"`
	Name            string                 `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	User            *User                  `protobuf:"bytes,7,opt,name=user,proto3" json:"user,omitempty"`
	LastSeen        *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=last_seen,json=lastSeen,proto3" json:"last_seen,omitempty"`
	Expiry          *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=expiry,proto3" json:"expiry,omitempty"`
	PreAuthKey      *PreAuthKey            `protobuf:"bytes,11,opt,name=pre_auth_key,json=preAuthKey,proto3" json:"pre_auth_key,omitempty"`
	CreatedAt       *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	RegisterMethod  RegisterMethod         `protobuf:"varint,13,opt,name=register_method,json=registerMethod,proto3,enum=headscale.v1.RegisterMethod" json:"register_method,omitempty"`
	ForcedTags      []string               `protobuf:"bytes,18,rep,name=forced_tags,json=forcedTags,proto3" json:"forced_tags,omitempty"`
	InvalidTags     []string               `protobuf:"bytes,19,rep,name=invalid_tags,json=invalidTags,proto3" json:"invalid_tags,omitempty"`
	ValidTags       []string               `protobuf:"bytes,20,rep,name=valid_tags,json=validTags,proto3" json:"valid_tags,omitempty"`
	GivenName       string                 `protobuf:"bytes,21,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	Online          bool                   `protobuf:"varint,22,opt,name=online,proto3" json:"online,omitempty"`
	ApprovedRoutes  []string               `protobuf:"bytes,23,rep,name=approved_routes,json=approvedRoutes,proto3" json:"approved_routes,omitempty"`
	AvailableRoutes []string               `protobuf:"bytes,24,rep,name=available_routes,json=availableRoutes,proto3" json:"available_routes,omitempty"`
	SubnetRoutes    []string               `protobuf:"bytes,25,rep,name=subnet_routes,json=subnetRoutes,proto3" json:"subnet_routes,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Node) Reset() {
	*x = Node{}
	mi := &file_headscale_v1_node_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{0}
}

func (x *Node) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Node) GetMachineKey() string {
	if x != nil {
		return x.MachineKey
	}
	return ""
}

func (x *Node) GetNodeKey() string {
	if x != nil {
		return x.NodeKey
	}
	return ""
}

func (x *Node) GetDiscoKey() string {
	if x != nil {
		return x.DiscoKey
	}
	return ""
}

func (x *Node) GetIpAddresses() []string {
	if x != nil {
		return x.IpAddresses
	}
	return nil
}

func (x *Node) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Node) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *Node) GetLastSeen() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSeen
	}
	return nil
}

func (x *Node) GetExpiry() *timestamppb.Timestamp {
	if x != nil {
		return x.Expiry
	}
	return nil
}

func (x *Node) GetPreAuthKey() *PreAuthKey {
	if x != nil {
		return x.PreAuthKey
	}
	return nil
}

func (x *Node) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Node) GetRegisterMethod() RegisterMethod {
	if x != nil {
		return x.RegisterMethod
	}
	return RegisterMethod_REGISTER_METHOD_UNSPECIFIED
}

func (x *Node) GetForcedTags() []string {
	if x != nil {
		return x.ForcedTags
	}
	return nil
}

func (x *Node) GetInvalidTags() []string {
	if x != nil {
		return x.InvalidTags
	}
	return nil
}

func (x *Node) GetValidTags() []string {
	if x != nil {
		return x.ValidTags
	}
	return nil
}

func (x *Node) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *Node) GetOnline() bool {
	if x != nil {
		return x.Online
	}
	return false
}

func (x *Node) GetApprovedRoutes() []string {
	if x != nil {
		return x.ApprovedRoutes
	}
	return nil
}

func (x *Node) GetAvailableRoutes() []string {
	if x != nil {
		return x.AvailableRoutes
	}
	return nil
}

func (x *Node) GetSubnetRoutes() []string {
	if x != nil {
		return x.SubnetRoutes
	}
	return nil
}

type RegisterNodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Key           string                 `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterNodeRequest) Reset() {
	*x = RegisterNodeRequest{}
	mi := &file_headscale_v1_node_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterNodeRequest) ProtoMessage() {}

func (x *RegisterNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterNodeRequest.ProtoReflect.Descriptor instead.
func (*RegisterNodeRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{1}
}

func (x *RegisterNodeRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *RegisterNodeRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type RegisterNodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Node          *Node                  `protobuf:"bytes,1,opt,name=node,proto3" json:"node,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterNodeResponse) Reset() {
	*x = RegisterNodeResponse{}
	mi := &file_headscale_v1_node_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterNodeResponse) ProtoMessage() {}

func (x *RegisterNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterNodeResponse.ProtoReflect.Descriptor instead.
func (*RegisterNodeResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{2}
}

func (x *RegisterNodeResponse) GetNode() *Node {
	if x != nil {
		return x.Node
	}
	return nil
}

type GetNodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeId        uint64                 `protobuf:"varint,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNodeRequest) Reset() {
	*x = GetNodeRequest{}
	mi := &file_headscale_v1_node_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeRequest) ProtoMessage() {}

func (x *GetNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeRequest.ProtoReflect.Descriptor instead.
func (*GetNodeRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{3}
}

func (x *GetNodeRequest) GetNodeId() uint64 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

type GetNodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Node          *Node                  `protobuf:"bytes,1,opt,name=node,proto3" json:"node,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNodeResponse) Reset() {
	*x = GetNodeResponse{}
	mi := &file_headscale_v1_node_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodeResponse) ProtoMessage() {}

func (x *GetNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodeResponse.ProtoReflect.Descriptor instead.
func (*GetNodeResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{4}
}

func (x *GetNodeResponse) GetNode() *Node {
	if x != nil {
		return x.Node
	}
	return nil
}

type SetTagsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeId        uint64                 `protobuf:"varint,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	Tags          []string               `protobuf:"bytes,2,rep,name=tags,proto3" json:"tags,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetTagsRequest) Reset() {
	*x = SetTagsRequest{}
	mi := &file_headscale_v1_node_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetTagsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTagsRequest) ProtoMessage() {}

func (x *SetTagsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTagsRequest.ProtoReflect.Descriptor instead.
func (*SetTagsRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{5}
}

func (x *SetTagsRequest) GetNodeId() uint64 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (x *SetTagsRequest) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

type SetTagsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Node          *Node                  `protobuf:"bytes,1,opt,name=node,proto3" json:"node,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetTagsResponse) Reset() {
	*x = SetTagsResponse{}
	mi := &file_headscale_v1_node_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetTagsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTagsResponse) ProtoMessage() {}

func (x *SetTagsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTagsResponse.ProtoReflect.Descriptor instead.
func (*SetTagsResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{6}
}

func (x *SetTagsResponse) GetNode() *Node {
	if x != nil {
		return x.Node
	}
	return nil
}

type SetApprovedRoutesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeId        uint64                 `protobuf:"varint,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	Routes        []string               `protobuf:"bytes,2,rep,name=routes,proto3" json:"routes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetApprovedRoutesRequest) Reset() {
	*x = SetApprovedRoutesRequest{}
	mi := &file_headscale_v1_node_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetApprovedRoutesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetApprovedRoutesRequest) ProtoMessage() {}

func (x *SetApprovedRoutesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetApprovedRoutesRequest.ProtoReflect.Descriptor instead.
func (*SetApprovedRoutesRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{7}
}

func (x *SetApprovedRoutesRequest) GetNodeId() uint64 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (x *SetApprovedRoutesRequest) GetRoutes() []string {
	if x != nil {
		return x.Routes
	}
	return nil
}

type SetApprovedRoutesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Node          *Node                  `protobuf:"bytes,1,opt,name=node,proto3" json:"node,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetApprovedRoutesResponse) Reset() {
	*x = SetApprovedRoutesResponse{}
	mi := &file_headscale_v1_node_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetApprovedRoutesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetApprovedRoutesResponse) ProtoMessage() {}

func (x *SetApprovedRoutesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetApprovedRoutesResponse.ProtoReflect.Descriptor instead.
func (*SetApprovedRoutesResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{8}
}

func (x *SetApprovedRoutesResponse) GetNode() *Node {
	if x != nil {
		return x.Node
	}
	return nil
}

type DeleteNodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeId        uint64                 `protobuf:"varint,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNodeRequest) Reset() {
	*x = DeleteNodeRequest{}
	mi := &file_headscale_v1_node_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNodeRequest) ProtoMessage() {}

func (x *DeleteNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNodeRequest.ProtoReflect.Descriptor instead.
func (*DeleteNodeRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteNodeRequest) GetNodeId() uint64 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

type DeleteNodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNodeResponse) Reset() {
	*x = DeleteNodeResponse{}
	mi := &file_headscale_v1_node_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNodeResponse) ProtoMessage() {}

func (x *DeleteNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNodeResponse.ProtoReflect.Descriptor instead.
func (*DeleteNodeResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{10}
}

type ExpireNodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeId        uint64                 `protobuf:"varint,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExpireNodeRequest) Reset() {
	*x = ExpireNodeRequest{}
	mi := &file_headscale_v1_node_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExpireNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpireNodeRequest) ProtoMessage() {}

func (x *ExpireNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpireNodeRequest.ProtoReflect.Descriptor instead.
func (*ExpireNodeRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{11}
}

func (x *ExpireNodeRequest) GetNodeId() uint64 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

type ExpireNodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Node          *Node                  `protobuf:"bytes,1,opt,name=node,proto3" json:"node,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExpireNodeResponse) Reset() {
	*x = ExpireNodeResponse{}
	mi := &file_headscale_v1_node_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExpireNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpireNodeResponse) ProtoMessage() {}

func (x *ExpireNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpireNodeResponse.ProtoReflect.Descriptor instead.
func (*ExpireNodeResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{12}
}

func (x *ExpireNodeResponse) GetNode() *Node {
	if x != nil {
		return x.Node
	}
	return nil
}

type RenameNodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeId        uint64                 `protobuf:"varint,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	NewName       string                 `protobuf:"bytes,2,opt,name=new_name,json=newName,proto3" json:"new_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RenameNodeRequest) Reset() {
	*x = RenameNodeRequest{}
	mi := &file_headscale_v1_node_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RenameNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenameNodeRequest) ProtoMessage() {}

func (x *RenameNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenameNodeRequest.ProtoReflect.Descriptor instead.
func (*RenameNodeRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{13}
}

func (x *RenameNodeRequest) GetNodeId() uint64 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (x *RenameNodeRequest) GetNewName() string {
	if x != nil {
		return x.NewName
	}
	return ""
}

type RenameNodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Node          *Node                  `protobuf:"bytes,1,opt,name=node,proto3" json:"node,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RenameNodeResponse) Reset() {
	*x = RenameNodeResponse{}
	mi := &file_headscale_v1_node_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RenameNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenameNodeResponse) ProtoMessage() {}

func (x *RenameNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenameNodeResponse.ProtoReflect.Descriptor instead.
func (*RenameNodeResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{14}
}

func (x *RenameNodeResponse) GetNode() *Node {
	if x != nil {
		return x.Node
	}
	return nil
}

type ListNodesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNodesRequest) Reset() {
	*x = ListNodesRequest{}
	mi := &file_headscale_v1_node_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNodesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodesRequest) ProtoMessage() {}

func (x *ListNodesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodesRequest.ProtoReflect.Descriptor instead.
func (*ListNodesRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{15}
}

func (x *ListNodesRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

type ListNodesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Nodes         []*Node                `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNodesResponse) Reset() {
	*x = ListNodesResponse{}
	mi := &file_headscale_v1_node_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNodesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodesResponse) ProtoMessage() {}

func (x *ListNodesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodesResponse.ProtoReflect.Descriptor instead.
func (*ListNodesResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{16}
}

func (x *ListNodesResponse) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

type MoveNodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeId        uint64                 `protobuf:"varint,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	User          uint64                 `protobuf:"varint,2,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MoveNodeRequest) Reset() {
	*x = MoveNodeRequest{}
	mi := &file_headscale_v1_node_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MoveNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoveNodeRequest) ProtoMessage() {}

func (x *MoveNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoveNodeRequest.ProtoReflect.Descriptor instead.
func (*MoveNodeRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{17}
}

func (x *MoveNodeRequest) GetNodeId() uint64 {
	if x != nil {
		return x.NodeId
	}
	return 0
}

func (x *MoveNodeRequest) GetUser() uint64 {
	if x != nil {
		return x.User
	}
	return 0
}

type MoveNodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Node          *Node                  `protobuf:"bytes,1,opt,name=node,proto3" json:"node,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MoveNodeResponse) Reset() {
	*x = MoveNodeResponse{}
	mi := &file_headscale_v1_node_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MoveNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoveNodeResponse) ProtoMessage() {}

func (x *MoveNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoveNodeResponse.ProtoReflect.Descriptor instead.
func (*MoveNodeResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{18}
}

func (x *MoveNodeResponse) GetNode() *Node {
	if x != nil {
		return x.Node
	}
	return nil
}

type DebugCreateNodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Key           string                 `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Routes        []string               `protobuf:"bytes,4,rep,name=routes,proto3" json:"routes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DebugCreateNodeRequest) Reset() {
	*x = DebugCreateNodeRequest{}
	mi := &file_headscale_v1_node_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DebugCreateNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugCreateNodeRequest) ProtoMessage() {}

func (x *DebugCreateNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebugCreateNodeRequest.ProtoReflect.Descriptor instead.
func (*DebugCreateNodeRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{19}
}

func (x *DebugCreateNodeRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *DebugCreateNodeRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *DebugCreateNodeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DebugCreateNodeRequest) GetRoutes() []string {
	if x != nil {
		return x.Routes
	}
	return nil
}

type DebugCreateNodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Node          *Node                  `protobuf:"bytes,1,opt,name=node,proto3" json:"node,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DebugCreateNodeResponse) Reset() {
	*x = DebugCreateNodeResponse{}
	mi := &file_headscale_v1_node_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DebugCreateNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugCreateNodeResponse) ProtoMessage() {}

func (x *DebugCreateNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebugCreateNodeResponse.ProtoReflect.Descriptor instead.
func (*DebugCreateNodeResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{20}
}

func (x *DebugCreateNodeResponse) GetNode() *Node {
	if x != nil {
		return x.Node
	}
	return nil
}

type BackfillNodeIPsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Confirmed     bool                   `protobuf:"varint,1,opt,name=confirmed,proto3" json:"confirmed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackfillNodeIPsRequest) Reset() {
	*x = BackfillNodeIPsRequest{}
	mi := &file_headscale_v1_node_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackfillNodeIPsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackfillNodeIPsRequest) ProtoMessage() {}

func (x *BackfillNodeIPsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackfillNodeIPsRequest.ProtoReflect.Descriptor instead.
func (*BackfillNodeIPsRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{21}
}

func (x *BackfillNodeIPsRequest) GetConfirmed() bool {
	if x != nil {
		return x.Confirmed
	}
	return false
}

type BackfillNodeIPsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Changes       []string               `protobuf:"bytes,1,rep,name=changes,proto3" json:"changes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackfillNodeIPsResponse) Reset() {
	*x = BackfillNodeIPsResponse{}
	mi := &file_headscale_v1_node_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackfillNodeIPsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackfillNodeIPsResponse) ProtoMessage() {}

func (x *BackfillNodeIPsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_node_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackfillNodeIPsResponse.ProtoReflect.Descriptor instead.
func (*BackfillNodeIPsResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_node_proto_rawDescGZIP(), []int{22}
}

func (x *BackfillNodeIPsResponse) GetChanges() []string {
	if x != nil {
		return x.Changes
	}
	return nil
}

var File_headscale_v1_node_proto protoreflect.FileDescriptor

const file_headscale_v1_node_proto_rawDesc = "" +
	"\n" +
	"\x17headscale/v1/node.proto\x12\fheadscale.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1dheadscale/v1/preauthkey.proto\x1a\x17headscale/v1/user.proto\"\x98\x06\n" +
	"\x04Node\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1f\n" +
	"\vmachine_key\x18\x02 \x01(\tR\n" +
	"machineKey\x12\x19\n" +
	"\bnode_key\x18\x03 \x01(\tR\anodeKey\x12\x1b\n" +
	"\tdisco_key\x18\x04 \x01(\tR\bdiscoKey\x12!\n" +
	"\fip_addresses\x18\x05 \x03(\tR\vipAddresses\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\x12&\n" +
	"\x04user\x18\a \x01(\v2\x12.headscale.v1.UserR\x04user\x127\n" +
	"\tlast_seen\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\blastSeen\x122\n" +
	"\x06expiry\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\x06expiry\x12:\n" +
	"\fpre_auth_key\x18\v \x01(\v2\x18.headscale.v1.PreAuthKeyR\n" +
	"preAuthKey\x129\n" +
	"\n" +
	"created_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12E\n" +
	"\x0fregister_method\x18\r \x01(\x0e2\x1c.headscale.v1.RegisterMethodR\x0eregisterMethod\x12\x1f\n" +
	"\vforced_tags\x18\x12 \x03(\tR\n" +
	"forcedTags\x12!\n" +
	"\finvalid_tags\x18\x13 \x03(\tR\vinvalidTags\x12\x1d\n" +
	"\n" +
	"valid_tags\x18\x14 \x03(\tR\tvalidTags\x12\x1d\n" +
	"\n" +
	"given_name\x18\x15 \x01(\tR\tgivenName\x12\x16\n" +
	"\x06online\x18\x16 \x01(\bR\x06online\x12'\n" +
	"\x0fapproved_routes\x18\x17 \x03(\tR\x0eapprovedRoutes\x12)\n" +
	"\x10available_routes\x18\x18 \x03(\tR\x0favailableRoutes\x12#\n" +
	"\rsubnet_routes\x18\x19 \x03(\tR\fsubnetRoutesJ\x04\b\t\x10\n" +
	"J\x04\b\x0e\x10\x12\";\n" +
	"\x13RegisterNodeRequest\x12\x12\n" +
	"\x04user\x18\x01 \x01(\tR\x04user\x12\x10\n" +
	"\x03key\x18\x02 \x01(\tR\x03key\">\n" +
	"\x14RegisterNodeResponse\x12&\n" +
	"\x04node\x18\x01 \x01(\v2\x12.headscale.v1.NodeR\x04node\")\n" +
	"\x0eGetNodeRequest\x12\x17\n" +
	"\anode_id\x18\x01 \x01(\x04R\x06nodeId\"9\n" +
	"\x0fGetNodeResponse\x12&\n" +
	"\x04node\x18\x01 \x01(\v2\x12.headscale.v1.NodeR\x04node\"=\n" +
	"\x0eSetTagsRequest\x12\x17\n" +
	"\anode_id\x18\x01 \x01(\x04R\x06nodeId\x12\x12\n" +
	"\x04tags\x18\x02 \x03(\tR\x04tags\"9\n" +
	"\x0fSetTagsResponse\x12&\n" +
	"\x04node\x18\x01 \x01(\v2\x12.headscale.v1.NodeR\x04node\"K\n" +
	"\x18SetApprovedRoutesRequest\x12\x17\n" +
	"\anode_id\x18\x01 \x01(\x04R\x06nodeId\x12\x16\n" +
	"\x06routes\x18\x02 \x03(\tR\x06routes\"C\n" +
	"\x19SetApprovedRoutesResponse\x12&\n" +
	"\x04node\x18\x01 \x01(\v2\x12.headscale.v1.NodeR\x04node\",\n" +
	"\x11DeleteNodeRequest\x12\x17\n" +
	"\anode_id\x18\x01 \x01(\x04R\x06nodeId\"\x14\n" +
	"\x12DeleteNodeResponse\",\n" +
	"\x11ExpireNodeRequest\x12\x17\n" +
	"\anode_id\x18\x01 \x01(\x04R\x06nodeId\"<\n" +
	"\x12ExpireNodeResponse\x12&\n" +
	"\x04node\x18\x01 \x01(\v2\x12.headscale.v1.NodeR\x04node\"G\n" +
	"\x11RenameNodeRequest\x12\x17\n" +
	"\anode_id\x18\x01 \x01(\x04R\x06nodeId\x12\x19\n" +
	"\bnew_name\x18\x02 \x01(\tR\anewName\"<\n" +
	"\x12RenameNodeResponse\x12&\n" +
	"\x04node\x18\x01 \x01(\v2\x12.headscale.v1.NodeR\x04node\"&\n" +
	"\x10ListNodesRequest\x12\x12\n" +
	"\x04user\x18\x01 \x01(\tR\x04user\"=\n" +
	"\x11ListNodesResponse\x12(\n" +
	"\x05nodes\x18\x01 \x03(\v2\x12.headscale.v1.NodeR\x05nodes\">\n" +
	"\x0fMoveNodeRequest\x12\x17\n" +
	"\anode_id\x18\x01 \x01(\x04R\x06nodeId\x12\x12\n" +
	"\x04user\x18\x02 \x01(\x04R\x04user\":\n" +
	"\x10MoveNodeResponse\x12&\n" +
	"\x04node\x18\x01 \x01(\v2\x12.headscale.v1.NodeR\x04node\"j\n" +
	"\x16DebugCreateNodeRequest\x12\x12\n" +
	"\x04user\x18\x01 \x01(\tR\x04user\x12\x10\n" +
	"\x03key\x18\x02 \x01(\tR\x03key\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x16\n" +
	"\x06routes\x18\x04 \x03(\tR\x06routes\"A\n" +
	"\x17DebugCreateNodeResponse\x12&\n" +
	"\x04node\x18\x01 \x01(\v2\x12.headscale.v1.NodeR\x04node\"6\n" +
	"\x16BackfillNodeIPsRequest\x12\x1c\n" +
	"\tconfirmed\x18\x01 \x01(\bR\tconfirmed\"3\n" +
	"\x17BackfillNodeIPsResponse\x12\x18\n" +
	"\achanges\x18\x01 \x03(\tR\achanges*\x82\x01\n" +
	"\x0eRegisterMethod\x12\x1f\n" +
	"\x1bREGISTER_METHOD_UNSPECIFIED\x10\x00\x12\x1c\n" +
	"\x18REGISTER_METHOD_AUTH_KEY\x10\x01\x12\x17\n" +
	"\x13REGISTER_METHOD_CLI\x10\x02\x12\x18\n" +
	"\x14REGISTER_METHOD_OIDC\x10\x03B)Z'github.com/juanfont/headscale/gen/go/v1b\x06proto3"

var (
	file_headscale_v1_node_proto_rawDescOnce sync.Once
	file_headscale_v1_node_proto_rawDescData []byte
)

func file_headscale_v1_node_proto_rawDescGZIP() []byte {
	file_headscale_v1_node_proto_rawDescOnce.Do(func() {
		file_headscale_v1_node_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_headscale_v1_node_proto_rawDesc), len(file_headscale_v1_node_proto_rawDesc)))
	})
	return file_headscale_v1_node_proto_rawDescData
}

var file_headscale_v1_node_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_headscale_v1_node_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_headscale_v1_node_proto_goTypes = []any{
	(RegisterMethod)(0),               // 0: headscale.v1.RegisterMethod
	(*Node)(nil),                      // 1: headscale.v1.Node
	(*RegisterNodeRequest)(nil),       // 2: headscale.v1.RegisterNodeRequest
	(*RegisterNodeResponse)(nil),      // 3: headscale.v1.RegisterNodeResponse
	(*GetNodeRequest)(nil),            // 4: headscale.v1.GetNodeRequest
	(*GetNodeResponse)(nil),           // 5: headscale.v1.GetNodeResponse
	(*SetTagsRequest)(nil),            // 6: headscale.v1.SetTagsRequest
	(*SetTagsResponse)(nil),           // 7: headscale.v1.SetTagsResponse
	(*SetApprovedRoutesRequest)(nil),  // 8: headscale.v1.SetApprovedRoutesRequest
	(*SetApprovedRoutesResponse)(nil), // 9: headscale.v1.SetApprovedRoutesResponse
	(*DeleteNodeRequest)(nil),         // 10: headscale.v1.DeleteNodeRequest
	(*DeleteNodeResponse)(nil),        // 11: headscale.v1.DeleteNodeResponse
	(*ExpireNodeRequest)(nil),         // 12: headscale.v1.ExpireNodeRequest
	(*ExpireNodeResponse)(nil),        // 13: headscale.v1.ExpireNodeResponse
	(*RenameNodeRequest)(nil),         // 14: headscale.v1.RenameNodeRequest
	(*RenameNodeResponse)(nil),        // 15: headscale.v1.RenameNodeResponse
	(*ListNodesRequest)(nil),          // 16: headscale.v1.ListNodesRequest
	(*ListNodesResponse)(nil),         // 17: headscale.v1.ListNodesResponse
	(*MoveNodeRequest)(nil),           // 18: headscale.v1.MoveNodeRequest
	(*MoveNodeResponse)(nil),          // 19: headscale.v1.MoveNodeResponse
	(*DebugCreateNodeRequest)(nil),    // 20: headscale.v1.DebugCreateNodeRequest
	(*DebugCreateNodeResponse)(nil),   // 21: headscale.v1.DebugCreateNodeResponse
	(*BackfillNodeIPsRequest)(nil),    // 22: headscale.v1.BackfillNodeIPsRequest
	(*BackfillNodeIPsResponse)(nil),   // 23: headscale.v1.BackfillNodeIPsResponse
	(*User)(nil),                      // 24: headscale.v1.User
	(*timestamppb.Timestamp)(nil),     // 25: google.protobuf.Timestamp
	(*PreAuthKey)(nil),                // 26: headscale.v1.PreAuthKey
}
var file_headscale_v1_node_proto_depIdxs = []int32{
	24, // 0: headscale.v1.Node.user:type_name -> headscale.v1.User
	25, // 1: headscale.v1.Node.last_seen:type_name -> google.protobuf.Timestamp
	25, // 2: headscale.v1.Node.expiry:type_name -> google.protobuf.Timestamp
	26, // 3: headscale.v1.Node.pre_auth_key:type_name -> headscale.v1.PreAuthKey
	25, // 4: headscale.v1.Node.created_at:type_name -> google.protobuf.Timestamp
	0,  // 5: headscale.v1.Node.register_method:type_name -> headscale.v1.RegisterMethod
	1,  // 6: headscale.v1.RegisterNodeResponse.node:type_name -> headscale.v1.Node
	1,  // 7: headscale.v1.GetNodeResponse.node:type_name -> headscale.v1.Node
	1,  // 8: headscale.v1.SetTagsResponse.node:type_name -> headscale.v1.Node
	1,  // 9: headscale.v1.SetApprovedRoutesResponse.node:type_name -> headscale.v1.Node
	1,  // 10: headscale.v1.ExpireNodeResponse.node:type_name -> headscale.v1.Node
	1,  // 11: headscale.v1.RenameNodeResponse.node:type_name -> headscale.v1.Node
	1,  // 12: headscale.v1.ListNodesResponse.nodes:type_name -> headscale.v1.Node
	1,  // 13: headscale.v1.MoveNodeResponse.node:type_name -> headscale.v1.Node
	1,  // 14: headscale.v1.DebugCreateNodeResponse.node:type_name -> headscale.v1.Node
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_headscale_v1_node_proto_init() }
func file_headscale_v1_node_proto_init() {
	if File_headscale_v1_node_proto != nil {
		return
	}
	file_headscale_v1_preauthkey_proto_init()
	file_headscale_v1_user_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_headscale_v1_node_proto_rawDesc), len(file_headscale_v1_node_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_headscale_v1_node_proto_goTypes,
		DependencyIndexes: file_headscale_v1_node_proto_depIdxs,
		EnumInfos:         file_headscale_v1_node_proto_enumTypes,
		MessageInfos:      file_headscale_v1_node_proto_msgTypes,
	}.Build()
	File_headscale_v1_node_proto = out.File
	file_headscale_v1_node_proto_goTypes = nil
	file_headscale_v1_node_proto_depIdxs = nil
}
