syntax = "proto3";
package headscale.v1;
option go_package = "github.com/juanfont/headscale/gen/go/v1";

import "google/api/annotations.proto";

import "headscale/v1/user.proto";
import "headscale/v1/preauthkey.proto";
import "headscale/v1/node.proto";
import "headscale/v1/apikey.proto";
import "headscale/v1/policy.proto";

service HeadscaleService {
  // --- User start ---
  rpc CreateUser(CreateUserRequest) returns (CreateUserResponse) {
    option (google.api.http) = {
      post : "/api/v1/user"
      body : "*"
    };
  }

  rpc RenameUser(RenameUserRequest) returns (RenameUserResponse) {
    option (google.api.http) = {
      post : "/api/v1/user/{old_id}/rename/{new_name}"
    };
  }

  rpc DeleteUser(DeleteUserRequest) returns (DeleteUserResponse) {
    option (google.api.http) = {
      delete : "/api/v1/user/{id}"
    };
  }

  rpc ListUsers(ListUsersRequest) returns (ListUsersResponse) {
    option (google.api.http) = {
      get : "/api/v1/user"
    };
  }
  // --- User end ---

  // --- PreAuthKeys start ---
  rpc CreatePreAuthKey(CreatePreAuthKeyRequest)
      returns (CreatePreAuthKeyResponse) {
    option (google.api.http) = {
      post : "/api/v1/preauthkey"
      body : "*"
    };
  }

  rpc ExpirePreAuthKey(ExpirePreAuthKeyRequest)
      returns (ExpirePreAuthKeyResponse) {
    option (google.api.http) = {
      post : "/api/v1/preauthkey/expire"
      body : "*"
    };
  }

  rpc ListPreAuthKeys(ListPreAuthKeysRequest)
      returns (ListPreAuthKeysResponse) {
    option (google.api.http) = {
      get : "/api/v1/preauthkey"
    };
  }
  // --- PreAuthKeys end ---

  // --- Node start ---
  rpc DebugCreateNode(DebugCreateNodeRequest)
      returns (DebugCreateNodeResponse) {
    option (google.api.http) = {
      post : "/api/v1/debug/node"
      body : "*"
    };
  }

  rpc GetNode(GetNodeRequest) returns (GetNodeResponse) {
    option (google.api.http) = {
      get : "/api/v1/node/{node_id}"
    };
  }

  rpc SetTags(SetTagsRequest) returns (SetTagsResponse) {
    option (google.api.http) = {
      post : "/api/v1/node/{node_id}/tags"
      body : "*"
    };
  }

  rpc SetApprovedRoutes(SetApprovedRoutesRequest)
      returns (SetApprovedRoutesResponse) {
    option (google.api.http) = {
      post : "/api/v1/node/{node_id}/approve_routes"
      body : "*"
    };
  }

  rpc RegisterNode(RegisterNodeRequest) returns (RegisterNodeResponse) {
    option (google.api.http) = {
      post : "/api/v1/node/register"
    };
  }

  rpc DeleteNode(DeleteNodeRequest) returns (DeleteNodeResponse) {
    option (google.api.http) = {
      delete : "/api/v1/node/{node_id}"
    };
  }

  rpc ExpireNode(ExpireNodeRequest) returns (ExpireNodeResponse) {
    option (google.api.http) = {
      post : "/api/v1/node/{node_id}/expire"
    };
  }

  rpc RenameNode(RenameNodeRequest) returns (RenameNodeResponse) {
    option (google.api.http) = {
      post : "/api/v1/node/{node_id}/rename/{new_name}"
    };
  }

  rpc ListNodes(ListNodesRequest) returns (ListNodesResponse) {
    option (google.api.http) = {
      get : "/api/v1/node"
    };
  }

  rpc MoveNode(MoveNodeRequest) returns (MoveNodeResponse) {
    option (google.api.http) = {
      post : "/api/v1/node/{node_id}/user",
      body : "*"
    };
  }

  rpc BackfillNodeIPs(BackfillNodeIPsRequest)
      returns (BackfillNodeIPsResponse) {
    option (google.api.http) = {
      post : "/api/v1/node/backfillips"
    };
  }

  // --- Node end ---

  // --- ApiKeys start ---
  rpc CreateApiKey(CreateApiKeyRequest) returns (CreateApiKeyResponse) {
    option (google.api.http) = {
      post : "/api/v1/apikey"
      body : "*"
    };
  }

  rpc ExpireApiKey(ExpireApiKeyRequest) returns (ExpireApiKeyResponse) {
    option (google.api.http) = {
      post : "/api/v1/apikey/expire"
      body : "*"
    };
  }

  rpc ListApiKeys(ListApiKeysRequest) returns (ListApiKeysResponse) {
    option (google.api.http) = {
      get : "/api/v1/apikey"
    };
  }

  rpc DeleteApiKey(DeleteApiKeyRequest) returns (DeleteApiKeyResponse) {
    option (google.api.http) = {
      delete : "/api/v1/apikey/{prefix}"
    };
  }
  // --- ApiKeys end ---

  // --- Policy start ---
  rpc GetPolicy(GetPolicyRequest) returns (GetPolicyResponse) {
    option (google.api.http) = {
      get : "/api/v1/policy"
    };
  }

  rpc SetPolicy(SetPolicyRequest) returns (SetPolicyResponse) {
    option (google.api.http) = {
      put : "/api/v1/policy"
      body : "*"
    };
  }
  // --- Policy end ---

  // Implement Tailscale API
  // rpc GetDevice(GetDeviceRequest) returns(GetDeviceResponse) {
  //     option(google.api.http) = {
  //         get : "/api/v1/device/{id}"
  //     };
  // }

  // rpc DeleteDevice(DeleteDeviceRequest) returns(DeleteDeviceResponse) {
  //     option(google.api.http) = {
  //         delete : "/api/v1/device/{id}"
  //     };
  // }

  // rpc GetDeviceRoutes(GetDeviceRoutesRequest)
  // returns(GetDeviceRoutesResponse) {
  //     option(google.api.http) = {
  //         get : "/api/v1/device/{id}/routes"
  //     };
  // }

  // rpc EnableDeviceRoutes(EnableDeviceRoutesRequest)
  // returns(EnableDeviceRoutesResponse) {
  //     option(google.api.http) = {
  //         post : "/api/v1/device/{id}/routes"
  //     };
  // }
}
