// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: headscale/v1/device.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Latency struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LatencyMs     float32                `protobuf:"fixed32,1,opt,name=latency_ms,json=latencyMs,proto3" json:"latency_ms,omitempty"`
	Preferred     bool                   `protobuf:"varint,2,opt,name=preferred,proto3" json:"preferred,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Latency) Reset() {
	*x = Latency{}
	mi := &file_headscale_v1_device_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Latency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Latency) ProtoMessage() {}

func (x *Latency) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_device_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Latency.ProtoReflect.Descriptor instead.
func (*Latency) Descriptor() ([]byte, []int) {
	return file_headscale_v1_device_proto_rawDescGZIP(), []int{0}
}

func (x *Latency) GetLatencyMs() float32 {
	if x != nil {
		return x.LatencyMs
	}
	return 0
}

func (x *Latency) GetPreferred() bool {
	if x != nil {
		return x.Preferred
	}
	return false
}

type ClientSupports struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HairPinning   bool                   `protobuf:"varint,1,opt,name=hair_pinning,json=hairPinning,proto3" json:"hair_pinning,omitempty"`
	Ipv6          bool                   `protobuf:"varint,2,opt,name=ipv6,proto3" json:"ipv6,omitempty"`
	Pcp           bool                   `protobuf:"varint,3,opt,name=pcp,proto3" json:"pcp,omitempty"`
	Pmp           bool                   `protobuf:"varint,4,opt,name=pmp,proto3" json:"pmp,omitempty"`
	Udp           bool                   `protobuf:"varint,5,opt,name=udp,proto3" json:"udp,omitempty"`
	Upnp          bool                   `protobuf:"varint,6,opt,name=upnp,proto3" json:"upnp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientSupports) Reset() {
	*x = ClientSupports{}
	mi := &file_headscale_v1_device_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientSupports) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientSupports) ProtoMessage() {}

func (x *ClientSupports) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_device_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientSupports.ProtoReflect.Descriptor instead.
func (*ClientSupports) Descriptor() ([]byte, []int) {
	return file_headscale_v1_device_proto_rawDescGZIP(), []int{1}
}

func (x *ClientSupports) GetHairPinning() bool {
	if x != nil {
		return x.HairPinning
	}
	return false
}

func (x *ClientSupports) GetIpv6() bool {
	if x != nil {
		return x.Ipv6
	}
	return false
}

func (x *ClientSupports) GetPcp() bool {
	if x != nil {
		return x.Pcp
	}
	return false
}

func (x *ClientSupports) GetPmp() bool {
	if x != nil {
		return x.Pmp
	}
	return false
}

func (x *ClientSupports) GetUdp() bool {
	if x != nil {
		return x.Udp
	}
	return false
}

func (x *ClientSupports) GetUpnp() bool {
	if x != nil {
		return x.Upnp
	}
	return false
}

type ClientConnectivity struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Endpoints             []string               `protobuf:"bytes,1,rep,name=endpoints,proto3" json:"endpoints,omitempty"`
	Derp                  string                 `protobuf:"bytes,2,opt,name=derp,proto3" json:"derp,omitempty"`
	MappingVariesByDestIp bool                   `protobuf:"varint,3,opt,name=mapping_varies_by_dest_ip,json=mappingVariesByDestIp,proto3" json:"mapping_varies_by_dest_ip,omitempty"`
	Latency               map[string]*Latency    `protobuf:"bytes,4,rep,name=latency,proto3" json:"latency,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	ClientSupports        *ClientSupports        `protobuf:"bytes,5,opt,name=client_supports,json=clientSupports,proto3" json:"client_supports,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *ClientConnectivity) Reset() {
	*x = ClientConnectivity{}
	mi := &file_headscale_v1_device_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientConnectivity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientConnectivity) ProtoMessage() {}

func (x *ClientConnectivity) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_device_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientConnectivity.ProtoReflect.Descriptor instead.
func (*ClientConnectivity) Descriptor() ([]byte, []int) {
	return file_headscale_v1_device_proto_rawDescGZIP(), []int{2}
}

func (x *ClientConnectivity) GetEndpoints() []string {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *ClientConnectivity) GetDerp() string {
	if x != nil {
		return x.Derp
	}
	return ""
}

func (x *ClientConnectivity) GetMappingVariesByDestIp() bool {
	if x != nil {
		return x.MappingVariesByDestIp
	}
	return false
}

func (x *ClientConnectivity) GetLatency() map[string]*Latency {
	if x != nil {
		return x.Latency
	}
	return nil
}

func (x *ClientConnectivity) GetClientSupports() *ClientSupports {
	if x != nil {
		return x.ClientSupports
	}
	return nil
}

type GetDeviceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeviceRequest) Reset() {
	*x = GetDeviceRequest{}
	mi := &file_headscale_v1_device_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceRequest) ProtoMessage() {}

func (x *GetDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_device_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceRequest.ProtoReflect.Descriptor instead.
func (*GetDeviceRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_device_proto_rawDescGZIP(), []int{3}
}

func (x *GetDeviceRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetDeviceResponse struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	Addresses                 []string               `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
	Id                        string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	User                      string                 `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	Name                      string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Hostname                  string                 `protobuf:"bytes,5,opt,name=hostname,proto3" json:"hostname,omitempty"`
	ClientVersion             string                 `protobuf:"bytes,6,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	UpdateAvailable           bool                   `protobuf:"varint,7,opt,name=update_available,json=updateAvailable,proto3" json:"update_available,omitempty"`
	Os                        string                 `protobuf:"bytes,8,opt,name=os,proto3" json:"os,omitempty"`
	Created                   *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created,proto3" json:"created,omitempty"`
	LastSeen                  *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=last_seen,json=lastSeen,proto3" json:"last_seen,omitempty"`
	KeyExpiryDisabled         bool                   `protobuf:"varint,11,opt,name=key_expiry_disabled,json=keyExpiryDisabled,proto3" json:"key_expiry_disabled,omitempty"`
	Expires                   *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=expires,proto3" json:"expires,omitempty"`
	Authorized                bool                   `protobuf:"varint,13,opt,name=authorized,proto3" json:"authorized,omitempty"`
	IsExternal                bool                   `protobuf:"varint,14,opt,name=is_external,json=isExternal,proto3" json:"is_external,omitempty"`
	MachineKey                string                 `protobuf:"bytes,15,opt,name=machine_key,json=machineKey,proto3" json:"machine_key,omitempty"`
	NodeKey                   string                 `protobuf:"bytes,16,opt,name=node_key,json=nodeKey,proto3" json:"node_key,omitempty"`
	BlocksIncomingConnections bool                   `protobuf:"varint,17,opt,name=blocks_incoming_connections,json=blocksIncomingConnections,proto3" json:"blocks_incoming_connections,omitempty"`
	EnabledRoutes             []string               `protobuf:"bytes,18,rep,name=enabled_routes,json=enabledRoutes,proto3" json:"enabled_routes,omitempty"`
	AdvertisedRoutes          []string               `protobuf:"bytes,19,rep,name=advertised_routes,json=advertisedRoutes,proto3" json:"advertised_routes,omitempty"`
	ClientConnectivity        *ClientConnectivity    `protobuf:"bytes,20,opt,name=client_connectivity,json=clientConnectivity,proto3" json:"client_connectivity,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *GetDeviceResponse) Reset() {
	*x = GetDeviceResponse{}
	mi := &file_headscale_v1_device_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceResponse) ProtoMessage() {}

func (x *GetDeviceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_device_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceResponse.ProtoReflect.Descriptor instead.
func (*GetDeviceResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_device_proto_rawDescGZIP(), []int{4}
}

func (x *GetDeviceResponse) GetAddresses() []string {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *GetDeviceResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetDeviceResponse) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *GetDeviceResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetDeviceResponse) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *GetDeviceResponse) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

func (x *GetDeviceResponse) GetUpdateAvailable() bool {
	if x != nil {
		return x.UpdateAvailable
	}
	return false
}

func (x *GetDeviceResponse) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *GetDeviceResponse) GetCreated() *timestamppb.Timestamp {
	if x != nil {
		return x.Created
	}
	return nil
}

func (x *GetDeviceResponse) GetLastSeen() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSeen
	}
	return nil
}

func (x *GetDeviceResponse) GetKeyExpiryDisabled() bool {
	if x != nil {
		return x.KeyExpiryDisabled
	}
	return false
}

func (x *GetDeviceResponse) GetExpires() *timestamppb.Timestamp {
	if x != nil {
		return x.Expires
	}
	return nil
}

func (x *GetDeviceResponse) GetAuthorized() bool {
	if x != nil {
		return x.Authorized
	}
	return false
}

func (x *GetDeviceResponse) GetIsExternal() bool {
	if x != nil {
		return x.IsExternal
	}
	return false
}

func (x *GetDeviceResponse) GetMachineKey() string {
	if x != nil {
		return x.MachineKey
	}
	return ""
}

func (x *GetDeviceResponse) GetNodeKey() string {
	if x != nil {
		return x.NodeKey
	}
	return ""
}

func (x *GetDeviceResponse) GetBlocksIncomingConnections() bool {
	if x != nil {
		return x.BlocksIncomingConnections
	}
	return false
}

func (x *GetDeviceResponse) GetEnabledRoutes() []string {
	if x != nil {
		return x.EnabledRoutes
	}
	return nil
}

func (x *GetDeviceResponse) GetAdvertisedRoutes() []string {
	if x != nil {
		return x.AdvertisedRoutes
	}
	return nil
}

func (x *GetDeviceResponse) GetClientConnectivity() *ClientConnectivity {
	if x != nil {
		return x.ClientConnectivity
	}
	return nil
}

type DeleteDeviceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDeviceRequest) Reset() {
	*x = DeleteDeviceRequest{}
	mi := &file_headscale_v1_device_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDeviceRequest) ProtoMessage() {}

func (x *DeleteDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_device_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDeviceRequest.ProtoReflect.Descriptor instead.
func (*DeleteDeviceRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_device_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteDeviceRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteDeviceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDeviceResponse) Reset() {
	*x = DeleteDeviceResponse{}
	mi := &file_headscale_v1_device_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDeviceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDeviceResponse) ProtoMessage() {}

func (x *DeleteDeviceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_device_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDeviceResponse.ProtoReflect.Descriptor instead.
func (*DeleteDeviceResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_device_proto_rawDescGZIP(), []int{6}
}

type GetDeviceRoutesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeviceRoutesRequest) Reset() {
	*x = GetDeviceRoutesRequest{}
	mi := &file_headscale_v1_device_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceRoutesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceRoutesRequest) ProtoMessage() {}

func (x *GetDeviceRoutesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_device_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceRoutesRequest.ProtoReflect.Descriptor instead.
func (*GetDeviceRoutesRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_device_proto_rawDescGZIP(), []int{7}
}

func (x *GetDeviceRoutesRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetDeviceRoutesResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	EnabledRoutes    []string               `protobuf:"bytes,1,rep,name=enabled_routes,json=enabledRoutes,proto3" json:"enabled_routes,omitempty"`
	AdvertisedRoutes []string               `protobuf:"bytes,2,rep,name=advertised_routes,json=advertisedRoutes,proto3" json:"advertised_routes,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetDeviceRoutesResponse) Reset() {
	*x = GetDeviceRoutesResponse{}
	mi := &file_headscale_v1_device_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceRoutesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceRoutesResponse) ProtoMessage() {}

func (x *GetDeviceRoutesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_device_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceRoutesResponse.ProtoReflect.Descriptor instead.
func (*GetDeviceRoutesResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_device_proto_rawDescGZIP(), []int{8}
}

func (x *GetDeviceRoutesResponse) GetEnabledRoutes() []string {
	if x != nil {
		return x.EnabledRoutes
	}
	return nil
}

func (x *GetDeviceRoutesResponse) GetAdvertisedRoutes() []string {
	if x != nil {
		return x.AdvertisedRoutes
	}
	return nil
}

type EnableDeviceRoutesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Routes        []string               `protobuf:"bytes,2,rep,name=routes,proto3" json:"routes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EnableDeviceRoutesRequest) Reset() {
	*x = EnableDeviceRoutesRequest{}
	mi := &file_headscale_v1_device_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnableDeviceRoutesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableDeviceRoutesRequest) ProtoMessage() {}

func (x *EnableDeviceRoutesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_device_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableDeviceRoutesRequest.ProtoReflect.Descriptor instead.
func (*EnableDeviceRoutesRequest) Descriptor() ([]byte, []int) {
	return file_headscale_v1_device_proto_rawDescGZIP(), []int{9}
}

func (x *EnableDeviceRoutesRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EnableDeviceRoutesRequest) GetRoutes() []string {
	if x != nil {
		return x.Routes
	}
	return nil
}

type EnableDeviceRoutesResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	EnabledRoutes    []string               `protobuf:"bytes,1,rep,name=enabled_routes,json=enabledRoutes,proto3" json:"enabled_routes,omitempty"`
	AdvertisedRoutes []string               `protobuf:"bytes,2,rep,name=advertised_routes,json=advertisedRoutes,proto3" json:"advertised_routes,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *EnableDeviceRoutesResponse) Reset() {
	*x = EnableDeviceRoutesResponse{}
	mi := &file_headscale_v1_device_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnableDeviceRoutesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableDeviceRoutesResponse) ProtoMessage() {}

func (x *EnableDeviceRoutesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_headscale_v1_device_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableDeviceRoutesResponse.ProtoReflect.Descriptor instead.
func (*EnableDeviceRoutesResponse) Descriptor() ([]byte, []int) {
	return file_headscale_v1_device_proto_rawDescGZIP(), []int{10}
}

func (x *EnableDeviceRoutesResponse) GetEnabledRoutes() []string {
	if x != nil {
		return x.EnabledRoutes
	}
	return nil
}

func (x *EnableDeviceRoutesResponse) GetAdvertisedRoutes() []string {
	if x != nil {
		return x.AdvertisedRoutes
	}
	return nil
}

var File_headscale_v1_device_proto protoreflect.FileDescriptor

const file_headscale_v1_device_proto_rawDesc = "" +
	"\n" +
	"\x19headscale/v1/device.proto\x12\fheadscale.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"F\n" +
	"\aLatency\x12\x1d\n" +
	"\n" +
	"latency_ms\x18\x01 \x01(\x02R\tlatencyMs\x12\x1c\n" +
	"\tpreferred\x18\x02 \x01(\bR\tpreferred\"\x91\x01\n" +
	"\x0eClientSupports\x12!\n" +
	"\fhair_pinning\x18\x01 \x01(\bR\vhairPinning\x12\x12\n" +
	"\x04ipv6\x18\x02 \x01(\bR\x04ipv6\x12\x10\n" +
	"\x03pcp\x18\x03 \x01(\bR\x03pcp\x12\x10\n" +
	"\x03pmp\x18\x04 \x01(\bR\x03pmp\x12\x10\n" +
	"\x03udp\x18\x05 \x01(\bR\x03udp\x12\x12\n" +
	"\x04upnp\x18\x06 \x01(\bR\x04upnp\"\xe3\x02\n" +
	"\x12ClientConnectivity\x12\x1c\n" +
	"\tendpoints\x18\x01 \x03(\tR\tendpoints\x12\x12\n" +
	"\x04derp\x18\x02 \x01(\tR\x04derp\x128\n" +
	"\x19mapping_varies_by_dest_ip\x18\x03 \x01(\bR\x15mappingVariesByDestIp\x12G\n" +
	"\alatency\x18\x04 \x03(\v2-.headscale.v1.ClientConnectivity.LatencyEntryR\alatency\x12E\n" +
	"\x0fclient_supports\x18\x05 \x01(\v2\x1c.headscale.v1.ClientSupportsR\x0eclientSupports\x1aQ\n" +
	"\fLatencyEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12+\n" +
	"\x05value\x18\x02 \x01(\v2\x15.headscale.v1.LatencyR\x05value:\x028\x01\"\"\n" +
	"\x10GetDeviceRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\xa0\x06\n" +
	"\x11GetDeviceResponse\x12\x1c\n" +
	"\taddresses\x18\x01 \x03(\tR\taddresses\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\x12\x12\n" +
	"\x04user\x18\x03 \x01(\tR\x04user\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x1a\n" +
	"\bhostname\x18\x05 \x01(\tR\bhostname\x12%\n" +
	"\x0eclient_version\x18\x06 \x01(\tR\rclientVersion\x12)\n" +
	"\x10update_available\x18\a \x01(\bR\x0fupdateAvailable\x12\x0e\n" +
	"\x02os\x18\b \x01(\tR\x02os\x124\n" +
	"\acreated\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\acreated\x127\n" +
	"\tlast_seen\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\blastSeen\x12.\n" +
	"\x13key_expiry_disabled\x18\v \x01(\bR\x11keyExpiryDisabled\x124\n" +
	"\aexpires\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\aexpires\x12\x1e\n" +
	"\n" +
	"authorized\x18\r \x01(\bR\n" +
	"authorized\x12\x1f\n" +
	"\vis_external\x18\x0e \x01(\bR\n" +
	"isExternal\x12\x1f\n" +
	"\vmachine_key\x18\x0f \x01(\tR\n" +
	"machineKey\x12\x19\n" +
	"\bnode_key\x18\x10 \x01(\tR\anodeKey\x12>\n" +
	"\x1bblocks_incoming_connections\x18\x11 \x01(\bR\x19blocksIncomingConnections\x12%\n" +
	"\x0eenabled_routes\x18\x12 \x03(\tR\renabledRoutes\x12+\n" +
	"\x11advertised_routes\x18\x13 \x03(\tR\x10advertisedRoutes\x12Q\n" +
	"\x13client_connectivity\x18\x14 \x01(\v2 .headscale.v1.ClientConnectivityR\x12clientConnectivity\"%\n" +
	"\x13DeleteDeviceRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\x16\n" +
	"\x14DeleteDeviceResponse\"(\n" +
	"\x16GetDeviceRoutesRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"m\n" +
	"\x17GetDeviceRoutesResponse\x12%\n" +
	"\x0eenabled_routes\x18\x01 \x03(\tR\renabledRoutes\x12+\n" +
	"\x11advertised_routes\x18\x02 \x03(\tR\x10advertisedRoutes\"C\n" +
	"\x19EnableDeviceRoutesRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06routes\x18\x02 \x03(\tR\x06routes\"p\n" +
	"\x1aEnableDeviceRoutesResponse\x12%\n" +
	"\x0eenabled_routes\x18\x01 \x03(\tR\renabledRoutes\x12+\n" +
	"\x11advertised_routes\x18\x02 \x03(\tR\x10advertisedRoutesB)Z'github.com/juanfont/headscale/gen/go/v1b\x06proto3"

var (
	file_headscale_v1_device_proto_rawDescOnce sync.Once
	file_headscale_v1_device_proto_rawDescData []byte
)

func file_headscale_v1_device_proto_rawDescGZIP() []byte {
	file_headscale_v1_device_proto_rawDescOnce.Do(func() {
		file_headscale_v1_device_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_headscale_v1_device_proto_rawDesc), len(file_headscale_v1_device_proto_rawDesc)))
	})
	return file_headscale_v1_device_proto_rawDescData
}

var file_headscale_v1_device_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_headscale_v1_device_proto_goTypes = []any{
	(*Latency)(nil),                    // 0: headscale.v1.Latency
	(*ClientSupports)(nil),             // 1: headscale.v1.ClientSupports
	(*ClientConnectivity)(nil),         // 2: headscale.v1.ClientConnectivity
	(*GetDeviceRequest)(nil),           // 3: headscale.v1.GetDeviceRequest
	(*GetDeviceResponse)(nil),          // 4: headscale.v1.GetDeviceResponse
	(*DeleteDeviceRequest)(nil),        // 5: headscale.v1.DeleteDeviceRequest
	(*DeleteDeviceResponse)(nil),       // 6: headscale.v1.DeleteDeviceResponse
	(*GetDeviceRoutesRequest)(nil),     // 7: headscale.v1.GetDeviceRoutesRequest
	(*GetDeviceRoutesResponse)(nil),    // 8: headscale.v1.GetDeviceRoutesResponse
	(*EnableDeviceRoutesRequest)(nil),  // 9: headscale.v1.EnableDeviceRoutesRequest
	(*EnableDeviceRoutesResponse)(nil), // 10: headscale.v1.EnableDeviceRoutesResponse
	nil,                                // 11: headscale.v1.ClientConnectivity.LatencyEntry
	(*timestamppb.Timestamp)(nil),      // 12: google.protobuf.Timestamp
}
var file_headscale_v1_device_proto_depIdxs = []int32{
	11, // 0: headscale.v1.ClientConnectivity.latency:type_name -> headscale.v1.ClientConnectivity.LatencyEntry
	1,  // 1: headscale.v1.ClientConnectivity.client_supports:type_name -> headscale.v1.ClientSupports
	12, // 2: headscale.v1.GetDeviceResponse.created:type_name -> google.protobuf.Timestamp
	12, // 3: headscale.v1.GetDeviceResponse.last_seen:type_name -> google.protobuf.Timestamp
	12, // 4: headscale.v1.GetDeviceResponse.expires:type_name -> google.protobuf.Timestamp
	2,  // 5: headscale.v1.GetDeviceResponse.client_connectivity:type_name -> headscale.v1.ClientConnectivity
	0,  // 6: headscale.v1.ClientConnectivity.LatencyEntry.value:type_name -> headscale.v1.Latency
	7,  // [7:7] is the sub-list for method output_type
	7,  // [7:7] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_headscale_v1_device_proto_init() }
func file_headscale_v1_device_proto_init() {
	if File_headscale_v1_device_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_headscale_v1_device_proto_rawDesc), len(file_headscale_v1_device_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_headscale_v1_device_proto_goTypes,
		DependencyIndexes: file_headscale_v1_device_proto_depIdxs,
		MessageInfos:      file_headscale_v1_device_proto_msgTypes,
	}.Build()
	File_headscale_v1_device_proto = out.File
	file_headscale_v1_device_proto_goTypes = nil
	file_headscale_v1_device_proto_depIdxs = nil
}
