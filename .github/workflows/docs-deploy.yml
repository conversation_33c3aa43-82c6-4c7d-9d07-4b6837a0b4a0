name: Deploy docs

on:
  push:
    branches:
      # Main branch for development docs
      - main

      # Doc maintenance branches
      - doc/[0-9]+.[0-9]+.[0-9]+
    tags:
      # Stable release tags
      - v[0-9]+.[0-9]+.[0-9]+
    paths:
      - "docs/**"
      - "mkdocs.yml"
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Install python
        uses: actions/setup-python@v5
        with:
          python-version: 3.x
      - name: Setup cache
        uses: actions/cache@v4
        with:
          key: ${{ github.ref }}
          path: .cache
      - name: Setup dependencies
        run: pip install -r docs/requirements.txt
      - name: Configure git
        run: |
          git config user.name github-actions
          git config user.email <EMAIL>
      - name: Deploy development docs
        if: github.ref == 'refs/heads/main'
        run: mike deploy --push development unstable
      - name: Deploy stable docs from doc branches
        if: startsWith(github.ref, 'refs/heads/doc/')
        run: mike deploy --push ${GITHUB_REF_NAME##*/}
      - name: Deploy stable docs from tag
        if: startsWith(github.ref, 'refs/tags/v')
        # This assumes that only newer tags are pushed
        run: mike deploy --push --update-aliases ${GITHUB_REF_NAME#v} stable latest
