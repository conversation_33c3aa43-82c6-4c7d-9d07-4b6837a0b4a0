name: 🐞 Bug
description: File a bug/issue
title: "[Bug] <title>"
labels: ["bug", "needs triage"]
body:
  - type: checkboxes
    attributes:
      label: Is this a support request?
      description: This issue tracker is for bugs and feature requests only. If you need help, please use ask in our Discord community
      options:
        - label: This is not a support request
          required: true
  - type: checkboxes
    attributes:
      label: Is there an existing issue for this?
      description: Please search to see if an issue already exists for the bug you encountered.
      options:
        - label: I have searched the existing issues
          required: true
  - type: textarea
    attributes:
      label: Current Behavior
      description: A concise description of what you're experiencing.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected Behavior
      description: A concise description of what you expected to happen.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps To Reproduce
      description: Steps to reproduce the behavior.
      placeholder: |
        1. In this environment...
        1. With this config...
        1. Run '...'
        1. See error...
    validations:
      required: true
  - type: textarea
    attributes:
      label: Environment
      description: |
        Please provide information about your environment.
        If you are using a container, always provide the headscale version and not only the Docker image version.
        Please do not put "latest".

        If you are experiencing a problem during an upgrade, please provide the versions of the old and new versions of Headscale and Tailscale.

        examples:
          - **OS**: Ubuntu 24.04
          - **Headscale version**: 0.24.3
          - **Tailscale version**: 1.80.0
      value: |
        - OS:
        - Headscale version:
        - Tailscale version:
      render: markdown
    validations:
      required: true
  - type: checkboxes
    attributes:
      label: Runtime environment
      options:
        - label: Headscale is behind a (reverse) proxy
          required: false
        - label: Headscale runs in a container
          required: false
  - type: textarea
    attributes:
      label: Debug information
      description: |
        Links? References? Anything that will give us more context about the issue you are encountering.
        If **any** of these are omitted we will likely close your issue, do **not** ignore them.

        - Client netmap dump (see below)
        - Policy configuration
        - Headscale configuration
        - Headscale log (with `trace` enabled)

        Dump the netmap of tailscale clients:
        `tailscale debug netmap > DESCRIPTIVE_NAME.json`

        Dump the status of tailscale clients:
        `tailscale status --json > DESCRIPTIVE_NAME.json`

        Get the logs of a Tailscale client that is not working as expected.
        `tailscale daemon-logs`

        Tip: You can attach images or log files by clicking this area to highlight it and then dragging files in.
        **Ensure** you use formatting for files you attach.
        Do **not** paste in long files.
    validations:
      required: true
