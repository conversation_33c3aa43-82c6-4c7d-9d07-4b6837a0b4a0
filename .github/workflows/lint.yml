name: Lint

on: [pull_request]

concurrency:
  group: ${{ github.workflow }}-$${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  golangci-lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
      - name: Get changed files
        id: changed-files
        uses: dorny/paths-filter@v3
        with:
          filters: |
            files:
              - '*.nix'
              - 'go.*'
              - '**/*.go'
              - 'integration_test/'
              - 'config-example.yaml'
      - uses: DeterminateSystems/nix-installer-action@main
        if: steps.changed-files.outputs.files == 'true'
      - uses: DeterminateSystems/magic-nix-cache-action@main
        if: steps.changed-files.outputs.files == 'true'

      - name: golangci-lint
        if: steps.changed-files.outputs.files == 'true'
        run: nix develop --command -- golangci-lint run --new-from-rev=${{github.event.pull_request.base.sha}} --out-format=colored-line-number

  prettier-lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
      - name: Get changed files
        id: changed-files
        uses: dorny/paths-filter@v3
        with:
          filters: |
            files:
              - '*.nix'
              - '**/*.md'
              - '**/*.yml'
              - '**/*.yaml'
              - '**/*.ts'
              - '**/*.js'
              - '**/*.sass'
              - '**/*.css'
              - '**/*.scss'
              - '**/*.html'
      - uses: DeterminateSystems/nix-installer-action@main
        if: steps.changed-files.outputs.files == 'true'
      - uses: DeterminateSystems/magic-nix-cache-action@main
        if: steps.changed-files.outputs.files == 'true'

      - name: Prettify code
        if: steps.changed-files.outputs.files == 'true'
        run: nix develop --command -- prettier --no-error-on-unmatched-pattern --ignore-unknown --check **/*.{ts,js,md,yaml,yml,sass,css,scss,html}

  proto-lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: DeterminateSystems/nix-installer-action@main
      - uses: DeterminateSystems/magic-nix-cache-action@main

      - name: Buf lint
        run: nix develop --command -- buf lint proto
