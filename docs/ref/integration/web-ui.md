# Web interfaces for headscale

!!! warning "Community contributions"

    This page contains community contributions. The projects listed here are not
    maintained by the headscale authors and are written by community members.

Headscale doesn't provide a built-in web interface but users may pick one from the available options.

| Name                   | Repository Link                                            | Description                                                                          |
| ---------------------- | ---------------------------------------------------------- | ------------------------------------------------------------------------------------ |
| headscale-ui           | [Github](https://github.com/gurucomputing/headscale-ui)    | A web frontend for the headscale Tailscale-compatible coordination server            |
| HeadscaleUi            | [GitHub](https://github.com/simcu/headscale-ui)            | A static headscale admin ui, no backend environment required                         |
| Headplane              | [GitHub](https://github.com/tale/headplane)                | An advanced Tailscale inspired frontend for headscale                                |
| headscale-admin        | [Github](https://github.com/GoodiesHQ/headscale-admin)     | Headscale-Admin is meant to be a simple, modern web interface for headscale          |
| ouroboros              | [Github](https://github.com/yellowsink/ouroboros)          | Ouroboros is designed for users to manage their own devices, rather than for admins  |
| unraid-headscale-admin | [Github](https://github.com/ich777/unraid-headscale-admin) | A simple headscale admin UI for Unraid, it offers Local (`docker exec`) and API Mode |

You can ask for support on our [Discord server](https://discord.gg/c84AZQhmpx) in the "web-interfaces" channel.
