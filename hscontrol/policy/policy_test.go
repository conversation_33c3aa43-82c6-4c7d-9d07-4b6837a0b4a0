package policy

import (
	"encoding/json"
	"fmt"
	"net/netip"
	"testing"
	"time"

	"github.com/juanfont/headscale/hscontrol/policy/matcher"

	"github.com/google/go-cmp/cmp"
	"github.com/juanfont/headscale/hscontrol/types"
	"github.com/juanfont/headscale/hscontrol/util"
	"github.com/rs/zerolog/log"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
	"tailscale.com/net/tsaddr"
	"tailscale.com/tailcfg"
	"tailscale.com/util/must"
)

var ap = func(ipStr string) *netip.Addr {
	ip := netip.MustParseAddr(ipStr)
	return &ip
}

var p = func(prefStr string) netip.Prefix {
	ip := netip.MustParsePrefix(prefStr)
	return ip
}

// hsExitNodeDestForTest is the list of destination IP ranges that are allowed when
// we use headscale "autogroup:internet".
var hsExitNodeDestForTest = []tailcfg.NetPortRange{
	{IP: "0.0.0.0/5", Ports: tailcfg.PortRangeAny},
	{IP: "*******/7", Ports: tailcfg.PortRangeAny},
	{IP: "********/8", Ports: tailcfg.PortRangeAny},
	{IP: "********/6", Ports: tailcfg.PortRangeAny},
	{IP: "********/4", Ports: tailcfg.PortRangeAny},
	{IP: "********/3", Ports: tailcfg.PortRangeAny},
	{IP: "********/3", Ports: tailcfg.PortRangeAny},
	{IP: "********/6", Ports: tailcfg.PortRangeAny},
	{IP: "100.0.0.0/10", Ports: tailcfg.PortRangeAny},
	{IP: "***********/9", Ports: tailcfg.PortRangeAny},
	{IP: "*********/8", Ports: tailcfg.PortRangeAny},
	{IP: "*********/7", Ports: tailcfg.PortRangeAny},
	{IP: "*********/5", Ports: tailcfg.PortRangeAny},
	{IP: "1********/4", Ports: tailcfg.PortRangeAny},
	{IP: "12*******/3", Ports: tailcfg.PortRangeAny},
	{IP: "160.0.0.0/5", Ports: tailcfg.PortRangeAny},
	{IP: "16*******/8", Ports: tailcfg.PortRangeAny},
	{IP: "*********/9", Ports: tailcfg.PortRangeAny},
	{IP: "***********/10", Ports: tailcfg.PortRangeAny},
	{IP: "***********/11", Ports: tailcfg.PortRangeAny},
	{IP: "***********/12", Ports: tailcfg.PortRangeAny},
	{IP: "***********/13", Ports: tailcfg.PortRangeAny},
	{IP: "***********/14", Ports: tailcfg.PortRangeAny},
	{IP: "***********/15", Ports: tailcfg.PortRangeAny},
	{IP: "***********/16", Ports: tailcfg.PortRangeAny},
	{IP: "170.0.0.0/7", Ports: tailcfg.PortRangeAny},
	{IP: "*********/12", Ports: tailcfg.PortRangeAny},
	{IP: "**********/11", Ports: tailcfg.PortRangeAny},
	{IP: "**********/10", Ports: tailcfg.PortRangeAny},
	{IP: "***********/9", Ports: tailcfg.PortRangeAny},
	{IP: "*********/8", Ports: tailcfg.PortRangeAny},
	{IP: "*********/7", Ports: tailcfg.PortRangeAny},
	{IP: "*********/4", Ports: tailcfg.PortRangeAny},
	{IP: "*********/9", Ports: tailcfg.PortRangeAny},
	{IP: "***********/11", Ports: tailcfg.PortRangeAny},
	{IP: "***********/13", Ports: tailcfg.PortRangeAny},
	{IP: "***********/16", Ports: tailcfg.PortRangeAny},
	{IP: "***********/15", Ports: tailcfg.PortRangeAny},
	{IP: "***********/14", Ports: tailcfg.PortRangeAny},
	{IP: "***********/12", Ports: tailcfg.PortRangeAny},
	{IP: "***********/10", Ports: tailcfg.PortRangeAny},
	{IP: "*********/8", Ports: tailcfg.PortRangeAny},
	{IP: "*********/7", Ports: tailcfg.PortRangeAny},
	{IP: "*********/6", Ports: tailcfg.PortRangeAny},
	{IP: "200.0.0.0/5", Ports: tailcfg.PortRangeAny},
	{IP: "20*******/4", Ports: tailcfg.PortRangeAny},
	{IP: "*********/3", Ports: tailcfg.PortRangeAny},
	{IP: "2000::/3", Ports: tailcfg.PortRangeAny},
}

func TestTheInternet(t *testing.T) {
	internetSet := util.TheInternet()

	internetPrefs := internetSet.Prefixes()

	for i := range internetPrefs {
		if internetPrefs[i].String() != hsExitNodeDestForTest[i].IP {
			t.Errorf(
				"prefix from internet set %q != hsExit list %q",
				internetPrefs[i].String(),
				hsExitNodeDestForTest[i].IP,
			)
		}
	}

	if len(internetPrefs) != len(hsExitNodeDestForTest) {
		t.Fatalf(
			"expected same length of prefixes, internet: %d, hsExit: %d",
			len(internetPrefs),
			len(hsExitNodeDestForTest),
		)
	}
}

func TestReduceFilterRules(t *testing.T) {
	users := types.Users{
		types.User{Model: gorm.Model{ID: 1}, Name: "mickael"},
		types.User{Model: gorm.Model{ID: 2}, Name: "user1"},
		types.User{Model: gorm.Model{ID: 3}, Name: "user2"},
		types.User{Model: gorm.Model{ID: 4}, Name: "user100"},
		types.User{Model: gorm.Model{ID: 5}, Name: "user3"},
	}

	tests := []struct {
		name  string
		node  *types.Node
		peers types.Nodes
		pol   string
		want  []tailcfg.FilterRule
	}{
		{
			name: "host1-can-reach-host2-no-rules",
			pol: `
{
  "acls": [
    {
      "action": "accept",
      "proto": "",
      "src": [
        "**********"
      ],
      "dst": [
        "**********:*"
      ]
    }
  ],
}
`,
			node: &types.Node{
				IPv4: ap("**********"),
				IPv6: ap("fd7a:115c:a1e0:ab12:4843:2222:6273:2221"),
				User: users[0],
			},
			peers: types.Nodes{
				&types.Node{
					IPv4: ap("**********"),
					IPv6: ap("fd7a:115c:a1e0:ab12:4843:2222:6273:2222"),
					User: users[0],
				},
			},
			want: []tailcfg.FilterRule{},
		},
		{
			name: "1604-subnet-routers-are-preserved",
			pol: `
{
  "groups": {
    "group:admins": [
      "user1@"
    ]
  },
  "acls": [
    {
      "action": "accept",
      "proto": "",
      "src": [
        "group:admins"
      ],
      "dst": [
        "group:admins:*"
      ]
    },
    {
      "action": "accept",
      "proto": "",
      "src": [
        "group:admins"
      ],
      "dst": [
        "*********/16:*"
      ]
    }
  ],
}
`,
			node: &types.Node{
				IPv4: ap("**********"),
				IPv6: ap("fd7a:115c:a1e0::1"),
				User: users[1],
				Hostinfo: &tailcfg.Hostinfo{
					RoutableIPs: []netip.Prefix{
						netip.MustParsePrefix("*********/16"),
					},
				},
			},
			peers: types.Nodes{
				&types.Node{
					IPv4: ap("**********"),
					IPv6: ap("fd7a:115c:a1e0::2"),
					User: users[1],
				},
			},
			want: []tailcfg.FilterRule{
				{
					SrcIPs: []string{
						"**********/32",
						"**********/32",
						"fd7a:115c:a1e0::1/128",
						"fd7a:115c:a1e0::2/128",
					},
					DstPorts: []tailcfg.NetPortRange{
						{
							IP:    "**********/32",
							Ports: tailcfg.PortRangeAny,
						},
						{
							IP:    "fd7a:115c:a1e0::1/128",
							Ports: tailcfg.PortRangeAny,
						},
					},
				},
				{
					SrcIPs: []string{
						"**********/32",
						"**********/32",
						"fd7a:115c:a1e0::1/128",
						"fd7a:115c:a1e0::2/128",
					},
					DstPorts: []tailcfg.NetPortRange{
						{
							IP:    "*********/16",
							Ports: tailcfg.PortRangeAny,
						},
					},
				},
			},
		},
		{
			name: "1786-reducing-breaks-exit-nodes-the-client",
			pol: `
{
  "groups": {
    "group:team": [
      "user3@",
      "user2@",
      "user1@"
    ]
  },
  "hosts": {
    "internal": "**********00/32"
  },
  "acls": [
    {
      "action": "accept",
      "proto": "",
      "src": [
        "group:team"
      ],
      "dst": [
        "internal:*"
      ]
    },
    {
      "action": "accept",
      "proto": "",
      "src": [
        "group:team"
      ],
      "dst": [
        "autogroup:internet:*"
      ]
    }
  ],
}
`,
			node: &types.Node{
				IPv4: ap("**********"),
				IPv6: ap("fd7a:115c:a1e0::1"),
				User: users[1],
			},
			peers: types.Nodes{
				&types.Node{
					IPv4: ap("**********"),
					IPv6: ap("fd7a:115c:a1e0::2"),
					User: users[2],
				},
				// "internal" exit node
				&types.Node{
					IPv4: ap("**********00"),
					IPv6: ap("fd7a:115c:a1e0::100"),
					User: users[3],
					Hostinfo: &tailcfg.Hostinfo{
						RoutableIPs: tsaddr.ExitRoutes(),
					},
				},
			},
			want: []tailcfg.FilterRule{},
		},
		{
			name: "1786-reducing-breaks-exit-nodes-the-exit",
			pol: `
{
  "groups": {
    "group:team": [
      "user3@",
      "user2@",
      "user1@"
    ]
  },
  "hosts": {
    "internal": "**********00/32"
  },
  "acls": [
    {
      "action": "accept",
      "proto": "",
      "src": [
        "group:team"
      ],
      "dst": [
        "internal:*"
      ]
    },
    {
      "action": "accept",
      "proto": "",
      "src": [
        "group:team"
      ],
      "dst": [
        "autogroup:internet:*"
      ]
    }
  ],
}
`,
			node: &types.Node{
				IPv4: ap("**********00"),
				IPv6: ap("fd7a:115c:a1e0::100"),
				User: users[3],
				Hostinfo: &tailcfg.Hostinfo{
					RoutableIPs: tsaddr.ExitRoutes(),
				},
			},
			peers: types.Nodes{
				&types.Node{
					IPv4: ap("**********"),
					IPv6: ap("fd7a:115c:a1e0::2"),
					User: users[2],
				},
				&types.Node{
					IPv4: ap("**********"),
					IPv6: ap("fd7a:115c:a1e0::1"),
					User: users[1],
				},
			},
			want: []tailcfg.FilterRule{
				{
					SrcIPs: []string{"**********/32", "**********/32", "fd7a:115c:a1e0::1/128", "fd7a:115c:a1e0::2/128"},
					DstPorts: []tailcfg.NetPortRange{
						{
							IP:    "**********00/32",
							Ports: tailcfg.PortRangeAny,
						},
						{
							IP:    "fd7a:115c:a1e0::100/128",
							Ports: tailcfg.PortRangeAny,
						},
					},
				},
				{
					SrcIPs:   []string{"**********/32", "**********/32", "fd7a:115c:a1e0::1/128", "fd7a:115c:a1e0::2/128"},
					DstPorts: hsExitNodeDestForTest,
				},
			},
		},
		{
			name: "1786-reducing-breaks-exit-nodes-the-example-from-issue",
			pol: `
{
  "groups": {
    "group:team": [
      "user3@",
      "user2@",
      "user1@"
    ]
  },
  "hosts": {
    "internal": "**********00/32"
  },
  "acls": [
    {
      "action": "accept",
      "proto": "",
      "src": [
        "group:team"
      ],
      "dst": [
        "internal:*"
      ]
    },
    {
      "action": "accept",
      "proto": "",
      "src": [
        "group:team"
      ],
      "dst": [
        "0.0.0.0/5:*",
        "*******/7:*",
        "********/8:*",
        "********/6:*",
        "********/4:*",
        "********/3:*",
        "********/2:*",
        "12*******/3:*",
        "160.0.0.0/5:*",
        "16*******/6:*",
        "*********/12:*",
        "**********/11:*",
        "**********/10:*",
        "***********/9:*",
        "*********/8:*",
        "*********/7:*",
        "*********/4:*",
        "*********/9:*",
        "***********/11:*",
        "***********/13:*",
        "***********/16:*",
        "***********/15:*",
        "***********/14:*",
        "***********/12:*",
        "***********/10:*",
        "*********/8:*",
        "*********/7:*",
        "*********/6:*",
        "200.0.0.0/5:*",
        "20*******/4:*"
      ]
    }
  ],
}
`,
			node: &types.Node{
				IPv4: ap("**********00"),
				IPv6: ap("fd7a:115c:a1e0::100"),
				User: users[3],
				Hostinfo: &tailcfg.Hostinfo{
					RoutableIPs: tsaddr.ExitRoutes(),
				},
			},
			peers: types.Nodes{
				&types.Node{
					IPv4: ap("**********"),
					IPv6: ap("fd7a:115c:a1e0::2"),
					User: users[2],
				},
				&types.Node{
					IPv4: ap("**********"),
					IPv6: ap("fd7a:115c:a1e0::1"),
					User: users[1],
				},
			},
			want: []tailcfg.FilterRule{
				{
					SrcIPs: []string{"**********/32", "**********/32", "fd7a:115c:a1e0::1/128", "fd7a:115c:a1e0::2/128"},
					DstPorts: []tailcfg.NetPortRange{
						{
							IP:    "**********00/32",
							Ports: tailcfg.PortRangeAny,
						},
						{
							IP:    "fd7a:115c:a1e0::100/128",
							Ports: tailcfg.PortRangeAny,
						},
					},
				},
				{
					SrcIPs: []string{"**********/32", "**********/32", "fd7a:115c:a1e0::1/128", "fd7a:115c:a1e0::2/128"},
					DstPorts: []tailcfg.NetPortRange{
						{IP: "0.0.0.0/5", Ports: tailcfg.PortRangeAny},
						{IP: "*******/7", Ports: tailcfg.PortRangeAny},
						{IP: "********/8", Ports: tailcfg.PortRangeAny},
						{IP: "********/6", Ports: tailcfg.PortRangeAny},
						{IP: "********/4", Ports: tailcfg.PortRangeAny},
						{IP: "********/3", Ports: tailcfg.PortRangeAny},
						{IP: "********/2", Ports: tailcfg.PortRangeAny},
						// This should not be included I believe, seems like
						// this is a bug in the v1 code.
						// For example:
						// If a src or dst includes "********/2:*", it will include 100.64/16 range, which
						// means that it will need to fetch the IPv6 addrs of the node to include the full range.
						// Clearly, if a user sets the dst to be "********/2:*", it is likely more of a exit node
						// and this would be strange behaviour.
						// TODO(kradalby): Remove before launch.
						{IP: "fd7a:115c:a1e0::1/128", Ports: tailcfg.PortRangeAny},
						{IP: "fd7a:115c:a1e0::2/128", Ports: tailcfg.PortRangeAny},
						{IP: "fd7a:115c:a1e0::100/128", Ports: tailcfg.PortRangeAny},
						// End
						{IP: "12*******/3", Ports: tailcfg.PortRangeAny},
						{IP: "160.0.0.0/5", Ports: tailcfg.PortRangeAny},
						{IP: "16*******/6", Ports: tailcfg.PortRangeAny},
						{IP: "*********/12", Ports: tailcfg.PortRangeAny},
						{IP: "**********/11", Ports: tailcfg.PortRangeAny},
						{IP: "**********/10", Ports: tailcfg.PortRangeAny},
						{IP: "***********/9", Ports: tailcfg.PortRangeAny},
						{IP: "*********/8", Ports: tailcfg.PortRangeAny},
						{IP: "*********/7", Ports: tailcfg.PortRangeAny},
						{IP: "*********/4", Ports: tailcfg.PortRangeAny},
						{IP: "*********/9", Ports: tailcfg.PortRangeAny},
						{IP: "***********/11", Ports: tailcfg.PortRangeAny},
						{IP: "***********/13", Ports: tailcfg.PortRangeAny},
						{IP: "***********/16", Ports: tailcfg.PortRangeAny},
						{IP: "***********/15", Ports: tailcfg.PortRangeAny},
						{IP: "***********/14", Ports: tailcfg.PortRangeAny},
						{IP: "***********/12", Ports: tailcfg.PortRangeAny},
						{IP: "***********/10", Ports: tailcfg.PortRangeAny},
						{IP: "*********/8", Ports: tailcfg.PortRangeAny},
						{IP: "*********/7", Ports: tailcfg.PortRangeAny},
						{IP: "*********/6", Ports: tailcfg.PortRangeAny},
						{IP: "200.0.0.0/5", Ports: tailcfg.PortRangeAny},
						{IP: "20*******/4", Ports: tailcfg.PortRangeAny},
					},
				},
			},
		},
		{
			name: "1786-reducing-breaks-exit-nodes-app-connector-like",
			pol: `
{
  "groups": {
    "group:team": [
      "user3@",
      "user2@",
      "user1@"
    ]
  },
  "hosts": {
    "internal": "**********00/32"
  },
  "acls": [
    {
      "action": "accept",
      "proto": "",
      "src": [
        "group:team"
      ],
      "dst": [
        "internal:*"
      ]
    },
    {
      "action": "accept",
      "proto": "",
      "src": [
        "group:team"
      ],
      "dst": [
        "*******/8:*",
        "********/8:*"
      ]
    }
  ],
}
`,
			node: &types.Node{
				IPv4: ap("**********00"),
				IPv6: ap("fd7a:115c:a1e0::100"),
				User: users[3],
				Hostinfo: &tailcfg.Hostinfo{
					RoutableIPs: []netip.Prefix{netip.MustParsePrefix("*******/16"), netip.MustParsePrefix("********/16")},
				},
			},
			peers: types.Nodes{
				&types.Node{
					IPv4: ap("**********"),
					IPv6: ap("fd7a:115c:a1e0::2"),
					User: users[2],
				},
				&types.Node{
					IPv4: ap("**********"),
					IPv6: ap("fd7a:115c:a1e0::1"),
					User: users[1],
				},
			},
			want: []tailcfg.FilterRule{
				{
					SrcIPs: []string{"**********/32", "**********/32", "fd7a:115c:a1e0::1/128", "fd7a:115c:a1e0::2/128"},
					DstPorts: []tailcfg.NetPortRange{
						{
							IP:    "**********00/32",
							Ports: tailcfg.PortRangeAny,
						},
						{
							IP:    "fd7a:115c:a1e0::100/128",
							Ports: tailcfg.PortRangeAny,
						},
					},
				},
				{
					SrcIPs: []string{"**********/32", "**********/32", "fd7a:115c:a1e0::1/128", "fd7a:115c:a1e0::2/128"},
					DstPorts: []tailcfg.NetPortRange{
						{
							IP:    "*******/8",
							Ports: tailcfg.PortRangeAny,
						},
						{
							IP:    "********/8",
							Ports: tailcfg.PortRangeAny,
						},
					},
				},
			},
		},
		{
			name: "1786-reducing-breaks-exit-nodes-app-connector-like2",
			pol: `
{
  "groups": {
    "group:team": [
      "user3@",
      "user2@",
      "user1@"
    ]
  },
  "hosts": {
    "internal": "**********00/32"
  },
  "acls": [
    {
      "action": "accept",
      "proto": "",
      "src": [
        "group:team"
      ],
      "dst": [
        "internal:*"
      ]
    },
    {
      "action": "accept",
      "proto": "",
      "src": [
        "group:team"
      ],
      "dst": [
        "*******/16:*",
        "********/16:*"
      ]
    }
  ],
}
`,
			node: &types.Node{
				IPv4: ap("**********00"),
				IPv6: ap("fd7a:115c:a1e0::100"),
				User: users[3],
				Hostinfo: &tailcfg.Hostinfo{
					RoutableIPs: []netip.Prefix{netip.MustParsePrefix("*******/8"), netip.MustParsePrefix("********/8")},
				},
			},
			peers: types.Nodes{
				&types.Node{
					IPv4: ap("**********"),
					IPv6: ap("fd7a:115c:a1e0::2"),
					User: users[2],
				},
				&types.Node{
					IPv4: ap("**********"),
					IPv6: ap("fd7a:115c:a1e0::1"),
					User: users[1],
				},
			},
			want: []tailcfg.FilterRule{
				{
					SrcIPs: []string{"**********/32", "**********/32", "fd7a:115c:a1e0::1/128", "fd7a:115c:a1e0::2/128"},
					DstPorts: []tailcfg.NetPortRange{
						{
							IP:    "**********00/32",
							Ports: tailcfg.PortRangeAny,
						},
						{
							IP:    "fd7a:115c:a1e0::100/128",
							Ports: tailcfg.PortRangeAny,
						},
					},
				},
				{
					SrcIPs: []string{"**********/32", "**********/32", "fd7a:115c:a1e0::1/128", "fd7a:115c:a1e0::2/128"},
					DstPorts: []tailcfg.NetPortRange{
						{
							IP:    "*******/16",
							Ports: tailcfg.PortRangeAny,
						},
						{
							IP:    "********/16",
							Ports: tailcfg.PortRangeAny,
						},
					},
				},
			},
		},
		{
			name: "1817-reduce-breaks-32-mask",
			pol: `
{
  "tagOwners": {
    "tag:access-servers": ["user100@"],
  },
  "groups": {
    "group:access": [
      "user1@"
    ]
  },
  "hosts": {
    "dns1": "***********/32",
    "vlan1": "**********/24"
  },
  "acls": [
    {
      "action": "accept",
      "proto": "",
      "src": [
        "group:access"
      ],
      "dst": [
        "tag:access-servers:*",
        "dns1:*"
      ]
    }
  ],
}
`,
			node: &types.Node{
				IPv4: ap("**********00"),
				IPv6: ap("fd7a:115c:a1e0::100"),
				User: users[3],
				Hostinfo: &tailcfg.Hostinfo{
					RoutableIPs: []netip.Prefix{netip.MustParsePrefix("**********/24")},
				},
				ForcedTags: []string{"tag:access-servers"},
			},
			peers: types.Nodes{
				&types.Node{
					IPv4: ap("**********"),
					IPv6: ap("fd7a:115c:a1e0::1"),
					User: users[1],
				},
			},
			want: []tailcfg.FilterRule{
				{
					SrcIPs: []string{"**********/32", "fd7a:115c:a1e0::1/128"},
					DstPorts: []tailcfg.NetPortRange{
						{
							IP:    "**********00/32",
							Ports: tailcfg.PortRangeAny,
						},
						{
							IP:    "fd7a:115c:a1e0::100/128",
							Ports: tailcfg.PortRangeAny,
						},
						{
							IP:    "***********/32",
							Ports: tailcfg.PortRangeAny,
						},
					},
				},
			},
		},
		{
			name: "2365-only-route-policy",
			pol: `
{
  "hosts": {
    "router": "**********/32",
    "node": "**********/32"
  },
  "acls": [
    {
      "action": "accept",
      "src": [
        "*"
      ],
      "dst": [
        "router:8000"
      ]
    },
    {
      "action": "accept",
      "src": [
        "node"
      ],
      "dst": [
        "**********/16:*"
      ]
    }
  ],
}
`,
			node: &types.Node{
				IPv4: ap("**********"),
				IPv6: ap("fd7a:115c:a1e0::2"),
				User: users[3],
			},
			peers: types.Nodes{
				&types.Node{
					IPv4: ap("**********"),
					IPv6: ap("fd7a:115c:a1e0::1"),
					User: users[1],
					Hostinfo: &tailcfg.Hostinfo{
						RoutableIPs: []netip.Prefix{p("**********/24"), p("**********/24"), p("**********/24")},
					},
					ApprovedRoutes: []netip.Prefix{p("**********/24"), p("**********/24"), p("**********/24")},
				},
			},
			want: []tailcfg.FilterRule{},
		},
	}

	for _, tt := range tests {
		for idx, pmf := range PolicyManagerFuncsForTest([]byte(tt.pol)) {
			version := idx + 1
			t.Run(fmt.Sprintf("%s-v%d", tt.name, version), func(t *testing.T) {
				var pm PolicyManager
				var err error
				pm, err = pmf(users, append(tt.peers, tt.node))
				require.NoError(t, err)
				got, _ := pm.Filter()
				t.Logf("full filter:\n%s", must.Get(json.MarshalIndent(got, "", "  ")))
				got = ReduceFilterRules(tt.node, got)

				if diff := cmp.Diff(tt.want, got); diff != "" {
					log.Trace().Interface("got", got).Msg("result")
					t.Errorf("TestReduceFilterRules() unexpected result (-want +got):\n%s", diff)
				}
			})
		}
	}
}

func TestReduceNodes(t *testing.T) {
	type args struct {
		nodes types.Nodes
		rules []tailcfg.FilterRule
		node  *types.Node
	}
	tests := []struct {
		name string
		args args
		want types.Nodes
	}{
		{
			name: "all hosts can talk to each other",
			args: args{
				nodes: types.Nodes{ // list of all nodes in the database
					&types.Node{
						ID:   1,
						IPv4: ap("**********"),
						User: types.User{Name: "joe"},
					},
					&types.Node{
						ID:   2,
						IPv4: ap("**********"),
						User: types.User{Name: "marc"},
					},
					&types.Node{
						ID:   3,
						IPv4: ap("**********"),
						User: types.User{Name: "mickael"},
					},
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{"**********", "**********", "**********"},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "*"},
						},
					},
				},
				node: &types.Node{ // current nodes
					ID:   1,
					IPv4: ap("**********"),
					User: types.User{Name: "joe"},
				},
			},
			want: types.Nodes{
				&types.Node{
					ID:   2,
					IPv4: ap("**********"),
					User: types.User{Name: "marc"},
				},
				&types.Node{
					ID:   3,
					IPv4: ap("**********"),
					User: types.User{Name: "mickael"},
				},
			},
		},
		{
			name: "One host can talk to another, but not all hosts",
			args: args{
				nodes: types.Nodes{ // list of all nodes in the database
					&types.Node{
						ID:   1,
						IPv4: ap("**********"),
						User: types.User{Name: "joe"},
					},
					&types.Node{
						ID:   2,
						IPv4: ap("**********"),
						User: types.User{Name: "marc"},
					},
					&types.Node{
						ID:   3,
						IPv4: ap("**********"),
						User: types.User{Name: "mickael"},
					},
				},
				rules: []tailcfg.FilterRule{ // list of all ACLRules registered
					{
						SrcIPs: []string{"**********", "**********", "**********"},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********"},
						},
					},
				},
				node: &types.Node{ // current nodes
					ID:   1,
					IPv4: ap("**********"),
					User: types.User{Name: "joe"},
				},
			},
			want: types.Nodes{
				&types.Node{
					ID:   2,
					IPv4: ap("**********"),
					User: types.User{Name: "marc"},
				},
			},
		},
		{
			name: "host cannot directly talk to destination, but return path is authorized",
			args: args{
				nodes: types.Nodes{ // list of all nodes in the database
					&types.Node{
						ID:   1,
						IPv4: ap("**********"),
						User: types.User{Name: "joe"},
					},
					&types.Node{
						ID:   2,
						IPv4: ap("**********"),
						User: types.User{Name: "marc"},
					},
					&types.Node{
						ID:   3,
						IPv4: ap("**********"),
						User: types.User{Name: "mickael"},
					},
				},
				rules: []tailcfg.FilterRule{ // list of all ACLRules registered
					{
						SrcIPs: []string{"**********"},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********"},
						},
					},
				},
				node: &types.Node{ // current nodes
					ID:   2,
					IPv4: ap("**********"),
					User: types.User{Name: "marc"},
				},
			},
			want: types.Nodes{
				&types.Node{
					ID:   3,
					IPv4: ap("**********"),
					User: types.User{Name: "mickael"},
				},
			},
		},
		{
			name: "rules allows all hosts to reach one destination",
			args: args{
				nodes: types.Nodes{ // list of all nodes in the database
					&types.Node{
						ID:   1,
						IPv4: ap("**********"),
						User: types.User{Name: "joe"},
					},
					&types.Node{
						ID:   2,
						IPv4: ap("**********"),
						User: types.User{Name: "marc"},
					},
					&types.Node{
						ID:   3,
						IPv4: ap("**********"),
						User: types.User{Name: "mickael"},
					},
				},
				rules: []tailcfg.FilterRule{ // list of all ACLRules registered
					{
						SrcIPs: []string{"*"},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********"},
						},
					},
				},
				node: &types.Node{ // current nodes
					ID:   1,
					IPv4: ap("**********"),
					User: types.User{Name: "joe"},
				},
			},
			want: types.Nodes{
				&types.Node{
					ID:   2,
					IPv4: ap("**********"),
					User: types.User{Name: "marc"},
				},
			},
		},
		{
			name: "rules allows all hosts to reach one destination, destination can reach all hosts",
			args: args{
				nodes: types.Nodes{ // list of all nodes in the database
					&types.Node{
						ID:   1,
						IPv4: ap("**********"),
						User: types.User{Name: "joe"},
					},
					&types.Node{
						ID:   2,
						IPv4: ap("**********"),
						User: types.User{Name: "marc"},
					},
					&types.Node{
						ID:   3,
						IPv4: ap("**********"),
						User: types.User{Name: "mickael"},
					},
				},
				rules: []tailcfg.FilterRule{ // list of all ACLRules registered
					{
						SrcIPs: []string{"*"},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********"},
						},
					},
				},
				node: &types.Node{ // current nodes
					ID:   2,
					IPv4: ap("**********"),
					User: types.User{Name: "marc"},
				},
			},
			want: types.Nodes{
				&types.Node{
					ID:   1,
					IPv4: ap("**********"),
					User: types.User{Name: "joe"},
				},
				&types.Node{
					ID:   3,
					IPv4: ap("**********"),
					User: types.User{Name: "mickael"},
				},
			},
		},
		{
			name: "rule allows all hosts to reach all destinations",
			args: args{
				nodes: types.Nodes{ // list of all nodes in the database
					&types.Node{
						ID:   1,
						IPv4: ap("**********"),
						User: types.User{Name: "joe"},
					},
					&types.Node{
						ID:   2,
						IPv4: ap("**********"),
						User: types.User{Name: "marc"},
					},
					&types.Node{
						ID:   3,
						IPv4: ap("**********"),
						User: types.User{Name: "mickael"},
					},
				},
				rules: []tailcfg.FilterRule{ // list of all ACLRules registered
					{
						SrcIPs: []string{"*"},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "*"},
						},
					},
				},
				node: &types.Node{ // current nodes
					ID:   2,
					IPv4: ap("**********"),
					User: types.User{Name: "marc"},
				},
			},
			want: types.Nodes{
				&types.Node{
					ID:   1,
					IPv4: ap("**********"),
					User: types.User{Name: "joe"},
				},
				&types.Node{
					ID:   3,
					IPv4: ap("**********"),
					User: types.User{Name: "mickael"},
				},
			},
		},
		{
			name: "without rule all communications are forbidden",
			args: args{
				nodes: types.Nodes{ // list of all nodes in the database
					&types.Node{
						ID:   1,
						IPv4: ap("**********"),
						User: types.User{Name: "joe"},
					},
					&types.Node{
						ID:   2,
						IPv4: ap("**********"),
						User: types.User{Name: "marc"},
					},
					&types.Node{
						ID:   3,
						IPv4: ap("**********"),
						User: types.User{Name: "mickael"},
					},
				},
				rules: []tailcfg.FilterRule{ // list of all ACLRules registered
				},
				node: &types.Node{ // current nodes
					ID:   2,
					IPv4: ap("**********"),
					User: types.User{Name: "marc"},
				},
			},
			want: nil,
		},
		{
			// Investigating 699
			// Found some nodes: [ts-head-8w6paa ts-unstable-lys2ib ts-head-upcrmb ts-unstable-rlwpvr] nodes=ts-head-8w6paa
			// ACL rules generated ACL=[{"DstPorts":[{"Bits":null,"IP":"*","Ports":{"First":0,"Last":65535}}],"SrcIPs":["fd7a:115c:a1e0::3","**********","fd7a:115c:a1e0::4","**********"]}]
			// ACL Cache Map={"**********":{"*":{}},"**********":{"*":{}},"fd7a:115c:a1e0::3":{"*":{}},"fd7a:115c:a1e0::4":{"*":{}}}
			name: "issue-699-broken-star",
			args: args{
				nodes: types.Nodes{ //
					&types.Node{
						ID:       1,
						Hostname: "ts-head-upcrmb",
						IPv4:     ap("**********"),
						IPv6:     ap("fd7a:115c:a1e0::3"),
						User:     types.User{Name: "user1"},
					},
					&types.Node{
						ID:       2,
						Hostname: "ts-unstable-rlwpvr",
						IPv4:     ap("**********"),
						IPv6:     ap("fd7a:115c:a1e0::4"),
						User:     types.User{Name: "user1"},
					},
					&types.Node{
						ID:       3,
						Hostname: "ts-head-8w6paa",
						IPv4:     ap("**********"),
						IPv6:     ap("fd7a:115c:a1e0::1"),
						User:     types.User{Name: "user2"},
					},
					&types.Node{
						ID:       4,
						Hostname: "ts-unstable-lys2ib",
						IPv4:     ap("**********"),
						IPv6:     ap("fd7a:115c:a1e0::2"),
						User:     types.User{Name: "user2"},
					},
				},
				rules: []tailcfg.FilterRule{ // list of all ACLRules registered
					{
						DstPorts: []tailcfg.NetPortRange{
							{
								IP:    "*",
								Ports: tailcfg.PortRange{First: 0, Last: 65535},
							},
						},
						SrcIPs: []string{
							"fd7a:115c:a1e0::3", "**********",
							"fd7a:115c:a1e0::4", "**********",
						},
					},
				},
				node: &types.Node{ // current nodes
					ID:       3,
					Hostname: "ts-head-8w6paa",
					IPv4:     ap("**********"),
					IPv6:     ap("fd7a:115c:a1e0::1"),
					User:     types.User{Name: "user2"},
				},
			},
			want: types.Nodes{
				&types.Node{
					ID:       1,
					Hostname: "ts-head-upcrmb",
					IPv4:     ap("**********"),
					IPv6:     ap("fd7a:115c:a1e0::3"),
					User:     types.User{Name: "user1"},
				},
				&types.Node{
					ID:       2,
					Hostname: "ts-unstable-rlwpvr",
					IPv4:     ap("**********"),
					IPv6:     ap("fd7a:115c:a1e0::4"),
					User:     types.User{Name: "user1"},
				},
			},
		},
		{
			name: "failing-edge-case-during-p3-refactor",
			args: args{
				nodes: []*types.Node{
					{
						ID:       1,
						IPv4:     ap("**********"),
						Hostname: "peer1",
						User:     types.User{Name: "mini"},
					},
					{
						ID:       2,
						IPv4:     ap("**********"),
						Hostname: "peer2",
						User:     types.User{Name: "peer2"},
					},
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{"**********/32"},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/32", Ports: tailcfg.PortRangeAny},
							{IP: "::/0", Ports: tailcfg.PortRangeAny},
						},
					},
				},
				node: &types.Node{
					ID:       0,
					IPv4:     ap("**********"),
					Hostname: "mini",
					User:     types.User{Name: "mini"},
				},
			},
			want: []*types.Node{
				{
					ID:       2,
					IPv4:     ap("**********"),
					Hostname: "peer2",
					User:     types.User{Name: "peer2"},
				},
			},
		},
		{
			name: "p4-host-in-netmap-user2-dest-bug",
			args: args{
				nodes: []*types.Node{
					{
						ID:       1,
						IPv4:     ap("**********"),
						Hostname: "user1-2",
						User:     types.User{Name: "user1"},
					},
					{
						ID:       0,
						IPv4:     ap("**********"),
						Hostname: "user1-1",
						User:     types.User{Name: "user1"},
					},
					{
						ID:       3,
						IPv4:     ap("**********"),
						Hostname: "user2-2",
						User:     types.User{Name: "user2"},
					},
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{
							"**********/32",
							"**********/32",
							"fd7a:115c:a1e0::3/128",
							"fd7a:115c:a1e0::4/128",
						},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/32", Ports: tailcfg.PortRangeAny},
							{IP: "**********/32", Ports: tailcfg.PortRangeAny},
							{IP: "fd7a:115c:a1e0::3/128", Ports: tailcfg.PortRangeAny},
							{IP: "fd7a:115c:a1e0::4/128", Ports: tailcfg.PortRangeAny},
						},
					},
					{
						SrcIPs: []string{
							"**********/32",
							"**********/32",
							"fd7a:115c:a1e0::1/128",
							"fd7a:115c:a1e0::2/128",
						},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/32", Ports: tailcfg.PortRangeAny},
							{IP: "**********/32", Ports: tailcfg.PortRangeAny},
							{IP: "fd7a:115c:a1e0::3/128", Ports: tailcfg.PortRangeAny},
							{IP: "fd7a:115c:a1e0::4/128", Ports: tailcfg.PortRangeAny},
						},
					},
				},
				node: &types.Node{
					ID:       2,
					IPv4:     ap("**********"),
					Hostname: "user-2-1",
					User:     types.User{Name: "user2"},
				},
			},
			want: []*types.Node{
				{
					ID:       1,
					IPv4:     ap("**********"),
					Hostname: "user1-2",
					User:     types.User{Name: "user1"},
				},
				{
					ID:       0,
					IPv4:     ap("**********"),
					Hostname: "user1-1",
					User:     types.User{Name: "user1"},
				},
				{
					ID:       3,
					IPv4:     ap("**********"),
					Hostname: "user2-2",
					User:     types.User{Name: "user2"},
				},
			},
		},
		{
			name: "p4-host-in-netmap-user1-dest-bug",
			args: args{
				nodes: []*types.Node{
					{
						ID:       1,
						IPv4:     ap("**********"),
						Hostname: "user1-2",
						User:     types.User{Name: "user1"},
					},
					{
						ID:       2,
						IPv4:     ap("**********"),
						Hostname: "user-2-1",
						User:     types.User{Name: "user2"},
					},
					{
						ID:       3,
						IPv4:     ap("**********"),
						Hostname: "user2-2",
						User:     types.User{Name: "user2"},
					},
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{
							"**********/32",
							"**********/32",
							"fd7a:115c:a1e0::1/128",
							"fd7a:115c:a1e0::2/128",
						},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/32", Ports: tailcfg.PortRangeAny},
							{IP: "**********/32", Ports: tailcfg.PortRangeAny},
							{IP: "fd7a:115c:a1e0::1/128", Ports: tailcfg.PortRangeAny},
							{IP: "fd7a:115c:a1e0::2/128", Ports: tailcfg.PortRangeAny},
						},
					},
					{
						SrcIPs: []string{
							"**********/32",
							"**********/32",
							"fd7a:115c:a1e0::1/128",
							"fd7a:115c:a1e0::2/128",
						},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/32", Ports: tailcfg.PortRangeAny},
							{IP: "**********/32", Ports: tailcfg.PortRangeAny},
							{IP: "fd7a:115c:a1e0::3/128", Ports: tailcfg.PortRangeAny},
							{IP: "fd7a:115c:a1e0::4/128", Ports: tailcfg.PortRangeAny},
						},
					},
				},
				node: &types.Node{
					ID:       0,
					IPv4:     ap("**********"),
					Hostname: "user1-1",
					User:     types.User{Name: "user1"},
				},
			},
			want: []*types.Node{
				{
					ID:       1,
					IPv4:     ap("**********"),
					Hostname: "user1-2",
					User:     types.User{Name: "user1"},
				},
				{
					ID:       2,
					IPv4:     ap("**********"),
					Hostname: "user-2-1",
					User:     types.User{Name: "user2"},
				},
				{
					ID:       3,
					IPv4:     ap("**********"),
					Hostname: "user2-2",
					User:     types.User{Name: "user2"},
				},
			},
		},
		{
			name: "subnet-router-with-only-route",
			args: args{
				nodes: []*types.Node{
					{
						ID:       1,
						IPv4:     ap("**********"),
						Hostname: "user1",
						User:     types.User{Name: "user1"},
					},
					{
						ID:       2,
						IPv4:     ap("**********"),
						Hostname: "router",
						User:     types.User{Name: "router"},
						Hostinfo: &tailcfg.Hostinfo{
							RoutableIPs: []netip.Prefix{netip.MustParsePrefix("*********/16")},
						},
						ApprovedRoutes: []netip.Prefix{netip.MustParsePrefix("*********/16")},
					},
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{
							"**********/32",
						},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "*********/16", Ports: tailcfg.PortRangeAny},
						},
					},
				},
				node: &types.Node{
					ID:       1,
					IPv4:     ap("**********"),
					Hostname: "user1",
					User:     types.User{Name: "user1"},
				},
			},
			want: []*types.Node{
				{
					ID:       2,
					IPv4:     ap("**********"),
					Hostname: "router",
					User:     types.User{Name: "router"},
					Hostinfo: &tailcfg.Hostinfo{
						RoutableIPs: []netip.Prefix{netip.MustParsePrefix("*********/16")},
					},
					ApprovedRoutes: []netip.Prefix{netip.MustParsePrefix("*********/16")},
				},
			},
		},
		{
			name: "subnet-router-with-only-route-smaller-mask-2181",
			args: args{
				nodes: []*types.Node{
					{
						ID:       1,
						IPv4:     ap("**********"),
						Hostname: "router",
						User:     types.User{Name: "router"},
						Hostinfo: &tailcfg.Hostinfo{
							RoutableIPs: []netip.Prefix{netip.MustParsePrefix("*********/16")},
						},
						ApprovedRoutes: []netip.Prefix{netip.MustParsePrefix("*********/16")},
					},
					{
						ID:       2,
						IPv4:     ap("**********"),
						Hostname: "node",
						User:     types.User{Name: "node"},
					},
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{
							"**********/32",
						},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "*********/32", Ports: tailcfg.PortRangeAny},
						},
					},
				},
				node: &types.Node{
					ID:       1,
					IPv4:     ap("**********"),
					Hostname: "router",
					User:     types.User{Name: "router"},
					Hostinfo: &tailcfg.Hostinfo{
						RoutableIPs: []netip.Prefix{netip.MustParsePrefix("*********/16")},
					},
					ApprovedRoutes: []netip.Prefix{netip.MustParsePrefix("*********/16")},
				},
			},
			want: []*types.Node{
				{
					ID:       2,
					IPv4:     ap("**********"),
					Hostname: "node",
					User:     types.User{Name: "node"},
				},
			},
		},
		{
			name: "node-to-subnet-router-with-only-route-smaller-mask-2181",
			args: args{
				nodes: []*types.Node{
					{
						ID:       1,
						IPv4:     ap("**********"),
						Hostname: "router",
						User:     types.User{Name: "router"},
						Hostinfo: &tailcfg.Hostinfo{
							RoutableIPs: []netip.Prefix{netip.MustParsePrefix("*********/16")},
						},
						ApprovedRoutes: []netip.Prefix{netip.MustParsePrefix("*********/16")},
					},
					{
						ID:       2,
						IPv4:     ap("**********"),
						Hostname: "node",
						User:     types.User{Name: "node"},
					},
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{
							"**********/32",
						},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "*********/32", Ports: tailcfg.PortRangeAny},
						},
					},
				},
				node: &types.Node{
					ID:       2,
					IPv4:     ap("**********"),
					Hostname: "node",
					User:     types.User{Name: "node"},
				},
			},
			want: []*types.Node{
				{
					ID:       1,
					IPv4:     ap("**********"),
					Hostname: "router",
					User:     types.User{Name: "router"},
					Hostinfo: &tailcfg.Hostinfo{
						RoutableIPs: []netip.Prefix{netip.MustParsePrefix("*********/16")},
					},
					ApprovedRoutes: []netip.Prefix{netip.MustParsePrefix("*********/16")},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			matchers := matcher.MatchesFromFilterRules(tt.args.rules)
			got := ReduceNodes(
				tt.args.node,
				tt.args.nodes,
				matchers,
			)
			if diff := cmp.Diff(tt.want, got, util.Comparers...); diff != "" {
				t.Errorf("FilterNodesByACL() unexpected result (-want +got):\n%s", diff)
			}
		})
	}
}

func TestSSHPolicyRules(t *testing.T) {
	users := []types.User{
		{Name: "user1", Model: gorm.Model{ID: 1}},
		{Name: "user2", Model: gorm.Model{ID: 2}},
		{Name: "user3", Model: gorm.Model{ID: 3}},
	}

	// Create standard node setups used across tests
	nodeUser1 := types.Node{
		Hostname: "user1-device",
		IPv4:     ap("**********"),
		UserID:   1,
		User:     users[0],
	}
	nodeUser2 := types.Node{
		Hostname: "user2-device",
		IPv4:     ap("**********"),
		UserID:   2,
		User:     users[1],
	}
	taggedServer := types.Node{
		Hostname:   "tagged-server",
		IPv4:       ap("**********"),
		UserID:     3,
		User:       users[2],
		ForcedTags: []string{"tag:server"},
	}
	taggedClient := types.Node{
		Hostname:   "tagged-client",
		IPv4:       ap("**********"),
		UserID:     2,
		User:       users[1],
		ForcedTags: []string{"tag:client"},
	}

	tests := []struct {
		name         string
		targetNode   types.Node
		peers        types.Nodes
		policy       string
		wantSSH      *tailcfg.SSHPolicy
		expectErr    bool
		errorMessage string

		// There are some tests that will not pass on V1 since we do not
		// have the same kind of error handling as V2, so we skip them.
		skipV1 bool
	}{
		{
			name:       "group-to-user",
			targetNode: nodeUser1,
			peers:      types.Nodes{&nodeUser2},
			policy: `{
				"groups": {
					"group:admins": ["user2@"]
				},
				"ssh": [
					{
						"action": "accept",
						"src": ["group:admins"],
						"dst": ["user1@"],
						"users": ["autogroup:nonroot"]
					}
				]
			}`,
			wantSSH: &tailcfg.SSHPolicy{Rules: []*tailcfg.SSHRule{
				{
					Principals: []*tailcfg.SSHPrincipal{
						{NodeIP: "**********"},
					},
					SSHUsers: map[string]string{
						"autogroup:nonroot": "=",
					},
					Action: &tailcfg.SSHAction{
						Accept:                   true,
						AllowAgentForwarding:     true,
						AllowLocalPortForwarding: true,
					},
				},
			}},

			// It looks like the group implementation in v1 is broken, so
			// we skip this test for v1 and not let it hold up v2 replacing it.
			skipV1: true,
		},
		{
			name:       "group-to-tag",
			targetNode: taggedServer,
			peers:      types.Nodes{&nodeUser1, &nodeUser2},
			policy: `{
				"tagOwners": {
				  "tag:server": ["user3@"],
				},
				"groups": {
					"group:users": ["user1@", "user2@"]
				},
				"ssh": [
					{
						"action": "accept",
						"src": ["group:users"],
						"dst": ["tag:server"],
						"users": ["autogroup:nonroot"]
					}
				]
			}`,
			wantSSH: &tailcfg.SSHPolicy{Rules: []*tailcfg.SSHRule{
				{
					Principals: []*tailcfg.SSHPrincipal{
						{NodeIP: "**********"},
						{NodeIP: "**********"},
					},
					SSHUsers: map[string]string{
						"autogroup:nonroot": "=",
					},
					Action: &tailcfg.SSHAction{
						Accept:                   true,
						AllowAgentForwarding:     true,
						AllowLocalPortForwarding: true,
					},
				},
			}},

			// It looks like the group implementation in v1 is broken, so
			// we skip this test for v1 and not let it hold up v2 replacing it.
			skipV1: true,
		},
		{
			name:       "tag-to-user",
			targetNode: nodeUser1,
			peers:      types.Nodes{&taggedClient},
			policy: `{
			    "tagOwners": {
					"tag:client": ["user1@"],
				},
				"ssh": [
					{
						"action": "accept",
						"src": ["tag:client"],
						"dst": ["user1@"],
						"users": ["autogroup:nonroot"]
					}
				]
			}`,
			wantSSH: &tailcfg.SSHPolicy{Rules: []*tailcfg.SSHRule{
				{
					Principals: []*tailcfg.SSHPrincipal{
						{NodeIP: "**********"},
					},
					SSHUsers: map[string]string{
						"autogroup:nonroot": "=",
					},
					Action: &tailcfg.SSHAction{
						Accept:                   true,
						AllowAgentForwarding:     true,
						AllowLocalPortForwarding: true,
					},
				},
			}},
		},
		{
			name:       "tag-to-tag",
			targetNode: taggedServer,
			peers:      types.Nodes{&taggedClient},
			policy: `{
				"tagOwners": {
					"tag:client": ["user2@"],
					"tag:server": ["user3@"],
				},
				"ssh": [
					{
						"action": "accept",
						"src": ["tag:client"],
						"dst": ["tag:server"],
						"users": ["autogroup:nonroot"]
					}
				]
			}`,
			wantSSH: &tailcfg.SSHPolicy{Rules: []*tailcfg.SSHRule{
				{
					Principals: []*tailcfg.SSHPrincipal{
						{NodeIP: "**********"},
					},
					SSHUsers: map[string]string{
						"autogroup:nonroot": "=",
					},
					Action: &tailcfg.SSHAction{
						Accept:                   true,
						AllowAgentForwarding:     true,
						AllowLocalPortForwarding: true,
					},
				},
			}},
		},
		{
			name:       "group-to-wildcard",
			targetNode: nodeUser1,
			peers:      types.Nodes{&nodeUser2, &taggedClient},
			policy: `{
				"groups": {
					"group:admins": ["user2@"]
				},
				"ssh": [
					{
						"action": "accept",
						"src": ["group:admins"],
						"dst": ["*"],
						"users": ["autogroup:nonroot"]
					}
				]
			}`,
			wantSSH: &tailcfg.SSHPolicy{Rules: []*tailcfg.SSHRule{
				{
					Principals: []*tailcfg.SSHPrincipal{
						{NodeIP: "**********"},
					},
					SSHUsers: map[string]string{
						"autogroup:nonroot": "=",
					},
					Action: &tailcfg.SSHAction{
						Accept:                   true,
						AllowAgentForwarding:     true,
						AllowLocalPortForwarding: true,
					},
				},
			}},

			// It looks like the group implementation in v1 is broken, so
			// we skip this test for v1 and not let it hold up v2 replacing it.
			skipV1: true,
		},
		{
			name:       "check-period-specified",
			targetNode: nodeUser1,
			peers:      types.Nodes{&taggedClient},
			policy: `{
				"tagOwners": {
					"tag:client": ["user1@"],
				},
				"ssh": [
					{
						"action": "check",
						"checkPeriod": "24h",
						"src": ["tag:client"],
						"dst": ["user1@"],
						"users": ["autogroup:nonroot"]
					}
				]
			}`,
			wantSSH: &tailcfg.SSHPolicy{Rules: []*tailcfg.SSHRule{
				{
					Principals: []*tailcfg.SSHPrincipal{
						{NodeIP: "**********"},
					},
					SSHUsers: map[string]string{
						"autogroup:nonroot": "=",
					},
					Action: &tailcfg.SSHAction{
						Accept:                   true,
						SessionDuration:          24 * time.Hour,
						AllowAgentForwarding:     true,
						AllowLocalPortForwarding: true,
					},
				},
			}},
		},
		{
			name:       "no-matching-rules",
			targetNode: nodeUser2,
			peers:      types.Nodes{&nodeUser1},
			policy: `{
			    "tagOwners": {
			    	"tag:client": ["user1@"],
			    },
				"ssh": [
					{
						"action": "accept",
						"src": ["tag:client"],
						"dst": ["user1@"],
						"users": ["autogroup:nonroot"]
					}
				]
			}`,
			wantSSH: &tailcfg.SSHPolicy{Rules: nil},
		},
		{
			name:       "invalid-action",
			targetNode: nodeUser1,
			peers:      types.Nodes{&nodeUser2},
			policy: `{
				"ssh": [
					{
						"action": "invalid",
						"src": ["group:admins"],
						"dst": ["user1@"],
						"users": ["autogroup:nonroot"]
					}
				]
			}`,
			expectErr:    true,
			errorMessage: `SSH action "invalid" is not valid, must be accept or check`,
			skipV1:       true,
		},
		{
			name:       "invalid-check-period",
			targetNode: nodeUser1,
			peers:      types.Nodes{&nodeUser2},
			policy: `{
				"ssh": [
					{
						"action": "check",
						"checkPeriod": "invalid",
						"src": ["group:admins"],
						"dst": ["user1@"],
						"users": ["autogroup:nonroot"]
					}
				]
			}`,
			expectErr:    true,
			errorMessage: "not a valid duration string",
			skipV1:       true,
		},
		{
			name:       "multiple-ssh-users-with-autogroup",
			targetNode: nodeUser1,
			peers:      types.Nodes{&taggedClient},
			policy: `{
			"tagOwners": {
				"tag:client": ["user1@"],
			},
        	"ssh": [
        	    {
        	        "action": "accept",
        	        "src": ["tag:client"],
        	        "dst": ["user1@"],
        	        "users": ["alice", "bob"]
        	    }
        	]
    }`,
			wantSSH: &tailcfg.SSHPolicy{Rules: []*tailcfg.SSHRule{
				{
					Principals: []*tailcfg.SSHPrincipal{
						{NodeIP: "**********"},
					},
					SSHUsers: map[string]string{
						"alice": "=",
						"bob":   "=",
					},
					Action: &tailcfg.SSHAction{
						Accept:                   true,
						AllowAgentForwarding:     true,
						AllowLocalPortForwarding: true,
					},
				},
			}},
		},
		{
			name:       "unsupported-autogroup",
			targetNode: nodeUser1,
			peers:      types.Nodes{&taggedClient},
			policy: `{
        "ssh": [
            {
                "action": "accept",
                "src": ["tag:client"],
                "dst": ["user1@"],
                "users": ["autogroup:invalid"]
            }
        ]
    }`,
			expectErr:    true,
			errorMessage: "autogroup \"autogroup:invalid\" is not supported",
			skipV1:       true,
		},
	}

	for _, tt := range tests {
		for idx, pmf := range PolicyManagerFuncsForTest([]byte(tt.policy)) {
			version := idx + 1
			t.Run(fmt.Sprintf("%s-v%d", tt.name, version), func(t *testing.T) {
				if version == 1 && tt.skipV1 {
					t.Skip()
				}

				var pm PolicyManager
				var err error
				pm, err = pmf(users, append(tt.peers, &tt.targetNode))

				if tt.expectErr {
					require.Error(t, err)
					require.Contains(t, err.Error(), tt.errorMessage)
					return
				}

				require.NoError(t, err)

				got, err := pm.SSHPolicy(&tt.targetNode)
				require.NoError(t, err)

				if diff := cmp.Diff(tt.wantSSH, got); diff != "" {
					t.Errorf("SSHPolicy() unexpected result (-want +got):\n%s", diff)
				}
			})
		}
	}
}
func TestReduceRoutes(t *testing.T) {
	type args struct {
		node   *types.Node
		routes []netip.Prefix
		rules  []tailcfg.FilterRule
	}
	tests := []struct {
		name string
		args args
		want []netip.Prefix
	}{
		{
			name: "node-can-access-all-routes",
			args: args{
				node: &types.Node{
					ID:   1,
					IPv4: ap("**********"),
					User: types.User{Name: "user1"},
				},
				routes: []netip.Prefix{
					netip.MustParsePrefix("10.0.0.0/24"),
					netip.MustParsePrefix("***********/24"),
					netip.MustParsePrefix("**********/16"),
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{"**********"},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "*"},
						},
					},
				},
			},
			want: []netip.Prefix{
				netip.MustParsePrefix("10.0.0.0/24"),
				netip.MustParsePrefix("***********/24"),
				netip.MustParsePrefix("**********/16"),
			},
		},
		{
			name: "node-can-access-specific-route",
			args: args{
				node: &types.Node{
					ID:   1,
					IPv4: ap("**********"),
					User: types.User{Name: "user1"},
				},
				routes: []netip.Prefix{
					netip.MustParsePrefix("10.0.0.0/24"),
					netip.MustParsePrefix("***********/24"),
					netip.MustParsePrefix("**********/16"),
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{"**********"},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "10.0.0.0/24"},
						},
					},
				},
			},
			want: []netip.Prefix{
				netip.MustParsePrefix("10.0.0.0/24"),
			},
		},
		{
			name: "node-can-access-multiple-specific-routes",
			args: args{
				node: &types.Node{
					ID:   1,
					IPv4: ap("**********"),
					User: types.User{Name: "user1"},
				},
				routes: []netip.Prefix{
					netip.MustParsePrefix("10.0.0.0/24"),
					netip.MustParsePrefix("***********/24"),
					netip.MustParsePrefix("**********/16"),
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{"**********"},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "10.0.0.0/24"},
							{IP: "***********/24"},
						},
					},
				},
			},
			want: []netip.Prefix{
				netip.MustParsePrefix("10.0.0.0/24"),
				netip.MustParsePrefix("***********/24"),
			},
		},
		{
			name: "node-can-access-overlapping-routes",
			args: args{
				node: &types.Node{
					ID:   1,
					IPv4: ap("**********"),
					User: types.User{Name: "user1"},
				},
				routes: []netip.Prefix{
					netip.MustParsePrefix("10.0.0.0/24"),
					netip.MustParsePrefix("10.0.0.0/16"), // Overlaps with the first one
					netip.MustParsePrefix("***********/24"),
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{"**********"},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "10.0.0.0/16"},
						},
					},
				},
			},
			want: []netip.Prefix{
				netip.MustParsePrefix("10.0.0.0/24"),
				netip.MustParsePrefix("10.0.0.0/16"),
			},
		},
		{
			name: "node-with-no-matching-rules",
			args: args{
				node: &types.Node{
					ID:   1,
					IPv4: ap("**********"),
					User: types.User{Name: "user1"},
				},
				routes: []netip.Prefix{
					netip.MustParsePrefix("10.0.0.0/24"),
					netip.MustParsePrefix("***********/24"),
					netip.MustParsePrefix("**********/16"),
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{"**********"}, // Different source IP
						DstPorts: []tailcfg.NetPortRange{
							{IP: "*"},
						},
					},
				},
			},
			want: nil,
		},
		{
			name: "node-with-both-ipv4-and-ipv6",
			args: args{
				node: &types.Node{
					ID:   1,
					IPv4: ap("**********"),
					IPv6: ap("fd7a:115c:a1e0::1"),
					User: types.User{Name: "user1"},
				},
				routes: []netip.Prefix{
					netip.MustParsePrefix("10.0.0.0/24"),
					netip.MustParsePrefix("2001:db8::/64"),
					netip.MustParsePrefix("***********/24"),
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{"fd7a:115c:a1e0::1"}, // IPv6 source
						DstPorts: []tailcfg.NetPortRange{
							{IP: "2001:db8::/64"}, // IPv6 destination
						},
					},
					{
						SrcIPs: []string{"**********"}, // IPv4 source
						DstPorts: []tailcfg.NetPortRange{
							{IP: "10.0.0.0/24"}, // IPv4 destination
						},
					},
				},
			},
			want: []netip.Prefix{
				netip.MustParsePrefix("10.0.0.0/24"),
				netip.MustParsePrefix("2001:db8::/64"),
			},
		},
		{
			name: "router-with-multiple-routes-and-node-with-specific-access",
			args: args{
				node: &types.Node{
					ID:   2,
					IPv4: ap("**********"), // Node IP
					User: types.User{Name: "node"},
				},
				routes: []netip.Prefix{
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{"*"}, // Any source
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********"}, // Router node
						},
					},
					{
						SrcIPs: []string{"**********"}, // Node IP
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/24"}, // Only one subnet allowed
						},
					},
				},
			},
			want: []netip.Prefix{
				netip.MustParsePrefix("**********/24"),
			},
		},
		{
			name: "node-with-access-to-one-subnet-and-partial-overlap",
			args: args{
				node: &types.Node{
					ID:   2,
					IPv4: ap("**********"),
					User: types.User{Name: "node"},
				},
				routes: []netip.Prefix{
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/16"), // Overlaps with the first one
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{"**********"},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/24"}, // Only specific subnet
						},
					},
				},
			},
			want: []netip.Prefix{
				netip.MustParsePrefix("**********/24"),
				netip.MustParsePrefix("**********/16"), // With current implementation, this is included because it overlaps with the allowed subnet
			},
		},
		{
			name: "node-with-access-to-wildcard-subnet",
			args: args{
				node: &types.Node{
					ID:   2,
					IPv4: ap("**********"),
					User: types.User{Name: "node"},
				},
				routes: []netip.Prefix{
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{"**********"},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "*********/16"}, // Broader subnet that includes all three
						},
					},
				},
			},
			want: []netip.Prefix{
				netip.MustParsePrefix("**********/24"),
				netip.MustParsePrefix("**********/24"),
				netip.MustParsePrefix("**********/24"),
			},
		},
		{
			name: "multiple-nodes-with-different-subnet-permissions",
			args: args{
				node: &types.Node{
					ID:   2,
					IPv4: ap("**********"),
					User: types.User{Name: "node"},
				},
				routes: []netip.Prefix{
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{"**********"}, // Different node
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/24"},
						},
					},
					{
						SrcIPs: []string{"**********"}, // Our node
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/24"},
						},
					},
					{
						SrcIPs: []string{"**********"}, // Different node
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/24"},
						},
					},
				},
			},
			want: []netip.Prefix{
				netip.MustParsePrefix("**********/24"),
			},
		},
		{
			name: "exactly-matching-users-acl-example",
			args: args{
				node: &types.Node{
					ID:   2,
					IPv4: ap("**********"), // node with IP **********
					User: types.User{Name: "node"},
				},
				routes: []netip.Prefix{
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
				},
				rules: []tailcfg.FilterRule{
					{
						// This represents the rule: action: accept, src: ["*"], dst: ["router:0"]
						SrcIPs: []string{"*"}, // Any source
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********"}, // Router IP
						},
					},
					{
						// This represents the rule: action: accept, src: ["node"], dst: ["**********/24:*"]
						SrcIPs: []string{"**********"}, // Node IP
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/24", Ports: tailcfg.PortRangeAny}, // All ports on this subnet
						},
					},
				},
			},
			want: []netip.Prefix{
				netip.MustParsePrefix("**********/24"),
			},
		},
		{
			name: "acl-all-source-nodes-can-access-router-only-node-can-access-**********-24",
			args: args{
				// When testing from router node's perspective
				node: &types.Node{
					ID:   1,
					IPv4: ap("**********"), // router with IP **********
					User: types.User{Name: "router"},
				},
				routes: []netip.Prefix{
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{"*"},
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********"}, // Router can be accessed by all
						},
					},
					{
						SrcIPs: []string{"**********"}, // Only node
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/24"}, // Can access this subnet
						},
					},
					// Add a rule for router to access its own routes
					{
						SrcIPs: []string{"**********"}, // Router node
						DstPorts: []tailcfg.NetPortRange{
							{IP: "*"}, // Can access everything
						},
					},
				},
			},
			// Router needs explicit rules to access routes
			want: []netip.Prefix{
				netip.MustParsePrefix("**********/24"),
				netip.MustParsePrefix("**********/24"),
				netip.MustParsePrefix("**********/24"),
			},
		},
		{
			name: "acl-specific-port-ranges-for-subnets",
			args: args{
				node: &types.Node{
					ID:   2,
					IPv4: ap("**********"), // node
					User: types.User{Name: "node"},
				},
				routes: []netip.Prefix{
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
				},
				rules: []tailcfg.FilterRule{
					{
						SrcIPs: []string{"**********"}, // node
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/24", Ports: tailcfg.PortRange{First: 22, Last: 22}}, // Only SSH
						},
					},
					{
						SrcIPs: []string{"**********"}, // node
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/24", Ports: tailcfg.PortRange{First: 80, Last: 80}}, // Only HTTP
						},
					},
				},
			},
			// Should get both subnets with specific port ranges
			want: []netip.Prefix{
				netip.MustParsePrefix("**********/24"),
				netip.MustParsePrefix("**********/24"),
			},
		},
		{
			name: "acl-order-of-rules-and-rule-specificity",
			args: args{
				node: &types.Node{
					ID:   2,
					IPv4: ap("**********"), // node
					User: types.User{Name: "node"},
				},
				routes: []netip.Prefix{
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
					netip.MustParsePrefix("**********/24"),
				},
				rules: []tailcfg.FilterRule{
					// First rule allows all traffic
					{
						SrcIPs: []string{"*"}, // Any source
						DstPorts: []tailcfg.NetPortRange{
							{IP: "*", Ports: tailcfg.PortRangeAny}, // Any destination and any port
						},
					},
					// Second rule is more specific but should be overridden by the first rule
					{
						SrcIPs: []string{"**********"}, // node
						DstPorts: []tailcfg.NetPortRange{
							{IP: "**********/24"},
						},
					},
				},
			},
			// Due to the first rule allowing all traffic, node should have access to all routes
			want: []netip.Prefix{
				netip.MustParsePrefix("**********/24"),
				netip.MustParsePrefix("**********/24"),
				netip.MustParsePrefix("**********/24"),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			matchers := matcher.MatchesFromFilterRules(tt.args.rules)
			got := ReduceRoutes(
				tt.args.node,
				tt.args.routes,
				matchers,
			)
			if diff := cmp.Diff(tt.want, got, util.Comparers...); diff != "" {
				t.Errorf("ReduceRoutes() unexpected result (-want +got):\n%s", diff)
			}
		})
	}
}
