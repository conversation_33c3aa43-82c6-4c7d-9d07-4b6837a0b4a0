name: GitHub Actions Version Updater

on:
  schedule:
    # Automatically run on every Sunday
    - cron: "0 0 * * 0"

jobs:
  build:
    if: github.repository == 'juanfont/headscale'
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          # [Required] Access token with `workflow` scope.
          token: ${{ secrets.WORKFLOW_SECRET }}

      - name: Run GitHub Actions Version Updater
        uses: saadmk11/github-actions-version-updater@v0.8.1
        with:
          # [Required] Access token with `workflow` scope.
          token: ${{ secrets.WORKFLOW_SECRET }}
